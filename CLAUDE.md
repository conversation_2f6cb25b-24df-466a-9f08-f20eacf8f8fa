# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-platform quantitative trading system that integrates MetaTrader 5 (MT5) and Binance platforms. The system uses modular design combining multiple technical analysis methods for intelligent market analysis and automated trading.

## Development Commands

### Running the Application
```bash
# Start the main trading system (runs both MT5 and Binance in separate threads)
python main.py

# Run Binance analysis only
python run_binance_analysis.py

# Setup AI environment (Windows)
setup_ai.bat
```

### Testing and Backtesting
```bash
# No standard test framework configured - tests are embedded in modules
# Backtesting available in MT5 platform:
# - platforms/mt5/mt5_backtest.py - Single strategy backtesting
# - platforms/mt5/batch_backtest.py - Batch backtesting across multiple parameters
```

### Dependencies
```bash
# Install all required packages
pip install -r requirements.txt
```

## Architecture Overview

### Core Structure
- **main.py**: Entry point running MT5 and Binance systems in parallel threads
- **core/**: Analysis engines and strategy implementations
  - `pattern_analyzer.py`: Technical pattern recognition
  - `unified_analyzer.py`: Unified analysis interface
  - `momentum_strategy.py`: Momentum-based trading strategies
  - `notifier.py`: Multi-channel notification system (WxPusher, Telegram)
  - `constants.py`: Trading signals, notification configs, API tokens

### Platform Modules
- **platforms/mt5/**: MetaTrader 5 integration
  - `mt5_trader.py`: Order execution and position management
  - `mt5_data.py`: Real-time and historical data acquisition
  - `mt5_backtest.py`: Strategy backtesting framework
  - `mt5_config.py`: MT5 connection and account settings
  - `mt5_position_manager.py`: Advanced position sizing and risk management

- **platforms/binance/**: Binance cryptocurrency integration  
  - `binance_analyzer.py`: Crypto market analysis and signal generation
  - `binance_data.py`: Market data retrieval and price monitoring

### Utility Modules
- **utils/**: Shared utilities
  - `log_utils.py`: Centralized logging configuration
  - `time_utils.py`: Time zone and scheduling utilities

## Key Technical Details

### Supported Assets
- **MT5**: XAUUSDc (Gold), BTCUSDc (Bitcoin), major forex pairs
- **Binance**: All major cryptocurrency trading pairs, spot trading

### Timeframes
- M15 (15 minutes)
- H1 (1 hour) 
- H4 (4 hours)

### Signal Types (from constants.py)
- PERFECT_PATTERN: Complete technical pattern formations
- TREND_BREAKOUT: Momentum breakout signals
- PATTERN_CONFIRMATION: Pattern validation signals
- PULLBACK_ENTRY: Retracement entry points
- DUCK_HEAD: "老鸭头" reversal pattern
- CUP_HANDLE: Cup and handle continuation pattern

### Notification System
- WxPusher integration for Chinese users
- Telegram bot support
- Configurable message templates for signals, errors, and system events
- Topic-based broadcasting

## Development Guidelines

### Code Quality Standards (from .cursor/rules)
- **Code-centric approach**: Focus strictly on code architecture, logic, and implementation
- **Robust error handling**: Include try-catch blocks, null checks, boundary conditions
- **Minimal scope**: Stay within current task requirements, avoid feature creep
- **Knowledge source transparency**: Document external library usage with version and verification dates

### Architecture Patterns
- Multi-threading for platform isolation (MT5 and Binance run independently)
- Strategy pattern for different analysis modules
- Observer pattern for notification system
- Factory pattern for different platform traders

## Important Configuration Notes

### Environment Setup
- Requires active MT5 terminal for MT5 operations
- Binance API keys needed for live trading (configured in binance modules)
- Notification tokens configured in `core/constants.py`
- Log files stored in `logs/` directory structure

### Threading Model
The application uses separate threads for MT5 and Binance operations to prevent platform-specific issues from affecting the entire system. Both threads share a global `running` flag for coordinated shutdown.

### Risk Management
- Position sizing algorithms in `mt5_position_manager.py`
- Kelly criterion implementation planned for future releases
- Multi-layered risk controls across all trading modules