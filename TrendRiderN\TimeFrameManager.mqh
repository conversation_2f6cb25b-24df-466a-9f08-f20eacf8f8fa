//+------------------------------------------------------------------+
//|                                         TimeFrameManager.mqh |
//|                        Copyright 2024, TrendRider Development |
//|                                             https://mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TrendRider Development"
#property link      "https://mql5.com"
#property version   "2.00"

//+------------------------------------------------------------------+
//| 时间框架配置结构体                                                 |
//+------------------------------------------------------------------+
struct TimeFrameConfig
{
   ENUM_TIMEFRAMES    base_timeframe;      // 基础交易周期
   ENUM_TIMEFRAMES    trend_timeframe;     // 趋势判断周期(向上3-5倍)
   ENUM_TIMEFRAMES    stoploss_timeframe;  // 止损设置周期(向下3-5倍)
   int                trend_multiplier;    // 趋势周期倍数
   int                stoploss_divisor;    // 止损周期除数
   
   // 构造函数
   TimeFrameConfig()
   {
      base_timeframe = PERIOD_CURRENT;
      trend_timeframe = PERIOD_CURRENT;
      stoploss_timeframe = PERIOD_CURRENT;
      trend_multiplier = 0;
      stoploss_divisor = 0;
   }
};

//+------------------------------------------------------------------+
//| 时间框架管理器类                                                   |
//+------------------------------------------------------------------+
class CTimeFrameManager
{
private:
   // 时间框架映射表(分钟为单位)
   int GetTimeFrameMinutes(ENUM_TIMEFRAMES timeframe);
   ENUM_TIMEFRAMES FindClosestTimeFrame(int target_minutes);
   
public:
   // 构造函数
   CTimeFrameManager();
   
   // 析构函数
   ~CTimeFrameManager();
   
   // 根据基础周期计算动态时间框架配置
   TimeFrameConfig CalculateTimeFrameConfig(ENUM_TIMEFRAMES base_tf);
   
   // 验证时间框架配置的有效性
   bool ValidateTimeFrameConfig(const TimeFrameConfig &config);
   
   // 获取时间框架描述文本
   string GetTimeFrameDescription(const TimeFrameConfig &config);
   
   // 检查时间框架之间的关系是否合理
   bool IsTimeFrameRelationshipValid(ENUM_TIMEFRAMES lower_tf, 
                                    ENUM_TIMEFRAMES base_tf, 
                                    ENUM_TIMEFRAMES higher_tf);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CTimeFrameManager::CTimeFrameManager()
{
   // 初始化时间框架管理器
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CTimeFrameManager::~CTimeFrameManager()
{
   // 清理资源
}

//+------------------------------------------------------------------+
//| 获取时间框架的分钟数                                               |
//+------------------------------------------------------------------+
int CTimeFrameManager::GetTimeFrameMinutes(ENUM_TIMEFRAMES timeframe)
{
   switch(timeframe)
   {
      case PERIOD_M1:  return 1;
      case PERIOD_M2:  return 2;
      case PERIOD_M3:  return 3;
      case PERIOD_M4:  return 4;
      case PERIOD_M5:  return 5;
      case PERIOD_M6:  return 6;
      case PERIOD_M10: return 10;
      case PERIOD_M12: return 12;
      case PERIOD_M15: return 15;
      case PERIOD_M20: return 20;
      case PERIOD_M30: return 30;
      case PERIOD_H1:  return 60;
      case PERIOD_H2:  return 120;
      case PERIOD_H3:  return 180;
      case PERIOD_H4:  return 240;
      case PERIOD_H6:  return 360;
      case PERIOD_H8:  return 480;
      case PERIOD_H12: return 720;
      case PERIOD_D1:  return 1440;
      case PERIOD_W1:  return 10080;
      case PERIOD_MN1: return 43200;
      default: return 0;
   }
}

//+------------------------------------------------------------------+
//| 查找最接近的时间框架                                               |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES CTimeFrameManager::FindClosestTimeFrame(int target_minutes)
{
   ENUM_TIMEFRAMES timeframes[] = {
      PERIOD_M1, PERIOD_M2, PERIOD_M3, PERIOD_M4, PERIOD_M5, PERIOD_M6,
      PERIOD_M10, PERIOD_M12, PERIOD_M15, PERIOD_M20, PERIOD_M30,
      PERIOD_H1, PERIOD_H2, PERIOD_H3, PERIOD_H4, PERIOD_H6, PERIOD_H8, PERIOD_H12,
      PERIOD_D1, PERIOD_W1, PERIOD_MN1
   };
   
   int closest_diff = INT_MAX;
   ENUM_TIMEFRAMES closest_tf = PERIOD_M1;
   
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      int tf_minutes = GetTimeFrameMinutes(timeframes[i]);
      int diff = MathAbs(tf_minutes - target_minutes);
      
      if(diff < closest_diff)
      {
         closest_diff = diff;
         closest_tf = timeframes[i];
      }
   }
   
   return closest_tf;
}

//+------------------------------------------------------------------+
//| 计算动态时间框架配置                                               |
//+------------------------------------------------------------------+
TimeFrameConfig CTimeFrameManager::CalculateTimeFrameConfig(ENUM_TIMEFRAMES base_tf)
{
   TimeFrameConfig config;
   config.base_timeframe = base_tf;
   
   int base_minutes = GetTimeFrameMinutes(base_tf);
   if(base_minutes == 0)
   {
      Print("错误: 无效的基础时间框架");
      return config;
   }
   
   // 计算趋势判断周期(向上3-5倍，取4倍)
   int trend_minutes = base_minutes * 4;
   config.trend_timeframe = FindClosestTimeFrame(trend_minutes);
   config.trend_multiplier = 4;
   
   // 计算止损设置周期(向下3-5倍，取4倍)
   int stoploss_minutes = base_minutes / 4;
   if(stoploss_minutes < 1) stoploss_minutes = 1; // 最小1分钟
   config.stoploss_timeframe = FindClosestTimeFrame(stoploss_minutes);
   config.stoploss_divisor = 4;
   
   // 特殊情况处理
   if(base_tf == PERIOD_M1)
   {
      // 1分钟周期特殊处理
      config.trend_timeframe = PERIOD_M5;
      config.stoploss_timeframe = PERIOD_M1;
      config.trend_multiplier = 5;
      config.stoploss_divisor = 1;
   }
   else if(base_tf == PERIOD_D1)
   {
      // 日线周期特殊处理
      config.trend_timeframe = PERIOD_W1;
      config.stoploss_timeframe = PERIOD_H4;
      config.trend_multiplier = 7;  // 一周7天
      config.stoploss_divisor = 6;  // 一天24小时/4小时=6
   }
   else if(base_tf == PERIOD_W1)
   {
      // 周线周期特殊处理
      config.trend_timeframe = PERIOD_MN1;
      config.stoploss_timeframe = PERIOD_D1;
      config.trend_multiplier = 4;  // 一月4周
      config.stoploss_divisor = 7;  // 一周7天
   }
   
   return config;
}

//+------------------------------------------------------------------+
//| 验证时间框架配置的有效性                                           |
//+------------------------------------------------------------------+
bool CTimeFrameManager::ValidateTimeFrameConfig(const TimeFrameConfig &config)
{
   int base_minutes = GetTimeFrameMinutes(config.base_timeframe);
   int trend_minutes = GetTimeFrameMinutes(config.trend_timeframe);
   int stoploss_minutes = GetTimeFrameMinutes(config.stoploss_timeframe);
   
   // 检查基本有效性
   if(base_minutes == 0 || trend_minutes == 0 || stoploss_minutes == 0)
      return false;
   
   // 检查时间框架关系: 止损周期 <= 基础周期 <= 趋势周期
   return (stoploss_minutes <= base_minutes && base_minutes <= trend_minutes);
}

//+------------------------------------------------------------------+
//| 获取时间框架配置描述                                               |
//+------------------------------------------------------------------+
string CTimeFrameManager::GetTimeFrameDescription(const TimeFrameConfig &config)
{
   return StringFormat("基础周期:%s | 趋势周期:%s(×%d) | 止损周期:%s(÷%d)",
                      EnumToString(config.base_timeframe),
                      EnumToString(config.trend_timeframe), config.trend_multiplier,
                      EnumToString(config.stoploss_timeframe), config.stoploss_divisor);
}

//+------------------------------------------------------------------+
//| 检查时间框架关系是否合理                                           |
//+------------------------------------------------------------------+
bool CTimeFrameManager::IsTimeFrameRelationshipValid(ENUM_TIMEFRAMES lower_tf, 
                                                     ENUM_TIMEFRAMES base_tf, 
                                                     ENUM_TIMEFRAMES higher_tf)
{
   int lower_minutes = GetTimeFrameMinutes(lower_tf);
   int base_minutes = GetTimeFrameMinutes(base_tf);
   int higher_minutes = GetTimeFrameMinutes(higher_tf);
   
   return (lower_minutes <= base_minutes && base_minutes <= higher_minutes);
}