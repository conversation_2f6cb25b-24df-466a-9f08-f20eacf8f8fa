高频剥头皮交易策略蓝图
核心定位
本蓝图旨在设计两种不同哲学、但同样追求在极短时间内捕获微小利润的自动化剥头皮交易策略（Scalping EA）。这两种策略均放弃对宏观市场方向的预测，专注于在价格的微观结构中寻找高概率的、可重复的盈利模式。

策略一：微观动量追踪策略 (Micro-Momentum Scalper)
1. 核心哲学
“惯性原理”：在金融市场的微观层面，价格的运动具有极强的惯性。如果一个方向上突然出现连续、快速的价格跳动（Ticks），那么在接下来的几秒钟内，价格有极高的概率会继续朝这个方向再移动一小步。本策略旨在捕捉这“一小步”的利润。

2. 交易工具与环境
数据源： 实时Tick数据流。这是策略的生命线。EA通过OnTick()函数来感知市场的每一次最小报价变动。

分析对象： Tick流的速度、密度和方向性。

交易环境（至关重要）：

低延迟VPS： 必须使用与经纪商服务器位于同一数据中心的VPS，确保网络延迟在1-5毫秒。

ECN/STP账户： 必须使用点差极低（最好接近0）且按佣金收费的账户。

高质量经纪商： 必须选择执行速度快、滑点极低的经纪商。

3. 策略逻辑 (逻辑闭环)
数据监控：

EA在内存中实时维护一个极短期的Tick队列（例如，最近的10个Ticks）。

持续计算这个队列中，价格向上跳动（Upticks）和向下跳动（Downticks）的数量。

入场信号 (Entry Signal):

买入信号： 当Tick队列中，Upticks的数量在极短时间内（如500毫秒内）压倒性地超过Downticks（例如，10个ticks里有8个或以上是Upticks），并且队列呈现出连续、无间断的上涨时，系统判定为“买方动量爆发”，立即执行市价买入。

卖出信号： 逻辑与买入相反。当Downticks的数量压倒性地超过Upticks时，判定为“卖方动量爆发”，立即执行市价卖出。

出场机制 (Exit Mechanism):

止盈 (Take Profit): 目标极其微小且固定。例如，黄金价格的3个点（$0.3）。一旦触及，立即无条件平仓。绝不贪婪是本策略的纪律核心。

止损 (Stop Loss): 同样微小且固定。例如，黄金价格的3个点（$0.3）。止损和止盈的比例严格维持在1:1或更高（如止损2点，止盈3点）。

时间止损 (Time Stop): 如果开仓后一定时间内（如15秒内）价格既没有触及止盈也没有触及止损，系统判定动能已消失，立即平仓离场，避免陷入盘整。

风险管理与过滤器：

点差过滤器： 在每次触发入场信号前，检查当前实时点差。如果点差大于一个预设的极小值（如黄金的2个点），则放弃本次交易。这是为了确保交易成本不会超过潜在利润。

并发控制： 系统在任何时候只允许持有一笔仓位，避免在混乱的信号中重复开仓。

策略二：均值回归剥头皮策略 (Mean Reversion Scalper)
1. 核心哲学
“物极必反”：在短时间尺度内（如1分钟），市场价格会围绕其短期价值中枢（移动平均线）波动。任何因随机事件导致的剧烈价格偏离，都将大概率被市场的内在力量拉回。本策略旨在捕捉价格从“极端”回归“正常”的过程。

2. 交易工具与环境
时间周期： M1（一分钟图）。

核心指标： 布林带 (Bollinger Bands, 例如周期20, 偏差2.0)。上轨和下轨是“极端”价格的动态边界，中轨是“价值中枢”。

过滤器指标：

长期移动平均线：EMA(200)，用于判断宏观趋势背景。

ATR (Average True Range)：ATR(14)，用于衡量市场波动性。

3. 策略逻辑 (逻辑闭环)
入场信号 (Entry Signal):

做空信号 (Sell): 当前K线的最高价触及或向上突破了布林带的上轨。

做多信号 (Buy): 当前K线的最低价触及或向下跌破了布林带的下轨。

出场机制 (Exit Mechanism):

止盈 (Take Profit): 当价格回调并触及布林带的中轨时，立即平仓。这是策略的核心盈利逻辑。

止损 (Stop Loss): 设置在触发信号的那根K线的最高点（对于空单）或最低点（对于多单）之外一个固定的安全距离（例如，黄金的3个点）。

核心过滤器 (确保盈利机会的关键):

趋势过滤器 (Trend Filter): 这是避免在单边趋势中“逆势摸顶/抄底”的关键防线。

规则： 只有当M1的EMA(200)低于当前布林带通道时，才允许执行做空信号（即价格从高位向EMA(200)方向回归）。

规则： 只有当M1的EMA(200)高于当前布林带通道时，才允许执行做多信号（即价格从低位向EMA(200)方向回归）。

规则： 当价格在EMA(200)附近缠绕时，说明大方向不明，暂停所有交易。

波动率过滤器 (Volatility Filter): 这是避免在重大新闻行情中被连续止损的“保险丝”。

规则： 在每次入场前，计算当前M1的ATR(14)值。如果该值显著大于过去N根K线（如100根）的平均ATR值（例如，大于1.5倍），则判定市场处于异常波动状态，暂停所有交易，直到波动率回归正常。