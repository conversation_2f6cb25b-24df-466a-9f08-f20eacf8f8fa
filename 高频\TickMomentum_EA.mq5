//+------------------------------------------------------------------+
//|                                         TickMomentum_EA.mq5 |
//|                    ⚡ Tick动量反转EA - 捕捉过度反应利润        |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "1.0"
#property description "基于Tick动量反转的超高频交易EA"

//--- 输入参数
input group "=== ⚡ 动量参数 ==="
input double   LotSize = 0.01;              // 交易手数
input int      MagicNumber = 999999;        // 魔术数字
input int      MomentumThreshold = 5;       // 动量阈值(连续tick数)
input double   MinPriceMove = 2.0;          // 最小价格移动(点)
input double   TakeProfit = 3.0;            // 止盈(点)
input double   StopLoss = 8.0;              // 止损(点)

input group "=== 🎯 反转信号参数 ==="
input double   OverboughtLevel = 0.8;       // 超买水平
input double   OversoldLevel = 0.2;         // 超卖水平
input int      LookbackPeriod = 20;         // 回看周期(tick数)
input bool     UseVolumeConfirm = true;     // 使用成交量确认

input group "=== 🛡️ 风控参数 ==="
input int      MaxPositions = 2;            // 最大持仓数
input double   MaxDailyLoss = 150.0;        // 日最大亏损($)
input int      MaxTradesPerHour = 10;       // 每小时最大交易数
input bool     EnableNewsFilter = true;     // 启用新闻过滤

//--- 全局变量
struct TickData {
    datetime time;
    double bid;
    double ask;
    double mid;
    long volume;
    double price_change;
};

TickData tick_history[100];  // 存储最近100个tick
int tick_index = 0;
int consecutive_up = 0;
int consecutive_down = 0;
double momentum_strength = 0;

// 交易统计
int hourly_trades = 0;
datetime last_hour = 0;
double daily_pnl = 0;
int total_signals = 0;
int successful_signals = 0;

//+------------------------------------------------------------------+
//| EA初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    EventSetMillisecondTimer(1);
    
    // 初始化tick历史
    ArrayInitialize(tick_history, 0);
    
    Print("⚡⚡⚡ Tick动量反转EA已启动！");
    Print("🎯 动量阈值: ", MomentumThreshold, " 连续tick");
    Print("📊 最小移动: ", MinPriceMove, " 点");
    Print("🚀 专注捕捉过度反应后的反转机会！");
    Print("========================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Tick函数
//+------------------------------------------------------------------+
void OnTick() {
    MqlTick current_tick;
    if(!SymbolInfoTick(_Symbol, current_tick)) return;
    
    // 更新tick历史
    UpdateTickHistory(current_tick);
    
    // 风控检查
    if(!RiskCheck()) return;
    
    // 分析动量
    AnalyzeMomentum();
    
    // 检测反转信号
    CheckReversalSignals(current_tick);
}

//+------------------------------------------------------------------+
//| 更新Tick历史数据
//+------------------------------------------------------------------+
void UpdateTickHistory(MqlTick &tick) {
    double mid_price = (tick.bid + tick.ask) / 2.0;
    
    // 计算价格变化
    double price_change = 0;
    if(tick_index > 0) {
        int prev_index = (tick_index - 1) % 100;
        price_change = mid_price - tick_history[prev_index].mid;
    }
    
    // 存储当前tick数据
    int current_index = tick_index % 100;
    tick_history[current_index].time = tick.time;
    tick_history[current_index].bid = tick.bid;
    tick_history[current_index].ask = tick.ask;
    tick_history[current_index].mid = mid_price;
    tick_history[current_index].volume = tick.volume;
    tick_history[current_index].price_change = price_change;
    
    tick_index++;
}

//+------------------------------------------------------------------+
//| 分析动量强度
//+------------------------------------------------------------------+
void AnalyzeMomentum() {
    if(tick_index < 2) return;
    
    int current_index = (tick_index - 1) % 100;
    double current_change = tick_history[current_index].price_change;
    
    // 统计连续方向
    if(current_change > 0) {
        consecutive_up++;
        consecutive_down = 0;
    } else if(current_change < 0) {
        consecutive_down++;
        consecutive_up = 0;
    }
    
    // 计算动量强度 (0-1之间)
    int max_consecutive = MathMax(consecutive_up, consecutive_down);
    momentum_strength = MathMin(1.0, (double)max_consecutive / (MomentumThreshold * 2));
    
    // 输出动量信息
    if(max_consecutive >= MomentumThreshold) {
        string direction = consecutive_up > consecutive_down ? "上涨" : "下跌";
        PrintFormat("🔥 强动量检测! %s连续%d个tick | 强度: %.2f", 
                   direction, max_consecutive, momentum_strength);
    }
}

//+------------------------------------------------------------------+
//| 检测反转信号
//+------------------------------------------------------------------+
void CheckReversalSignals(MqlTick &tick) {
    if(tick_index < LookbackPeriod) return;
    if(PositionsTotal() >= MaxPositions) return;
    
    // 计算价格移动幅度
    double price_range = CalculatePriceRange();
    if(price_range < MinPriceMove * _Point) return;
    
    // 检测超买超卖
    double rsi_like = CalculateTickRSI();
    
    // 反转信号条件
    bool bullish_reversal = (consecutive_down >= MomentumThreshold && 
                            rsi_like < OversoldLevel && 
                            momentum_strength > 0.6);
                            
    bool bearish_reversal = (consecutive_up >= MomentumThreshold && 
                            rsi_like > OverboughtLevel && 
                            momentum_strength > 0.6);
    
    // 成交量确认
    if(UseVolumeConfirm && !VolumeConfirmation()) return;
    
    // 执行交易
    if(bullish_reversal) {
        ExecuteReversalTrade(ORDER_TYPE_BUY, tick, "看涨反转");
        total_signals++;
    } else if(bearish_reversal) {
        ExecuteReversalTrade(ORDER_TYPE_SELL, tick, "看跌反转");
        total_signals++;
    }
}

//+------------------------------------------------------------------+
//| 计算价格区间
//+------------------------------------------------------------------+
double CalculatePriceRange() {
    if(tick_index < LookbackPeriod) return 0;
    
    double highest = 0, lowest = 999999;
    int start_index = MathMax(0, tick_index - LookbackPeriod);
    
    for(int i = start_index; i < tick_index; i++) {
        int index = i % 100;
        highest = MathMax(highest, tick_history[index].mid);
        lowest = MathMin(lowest, tick_history[index].mid);
    }
    
    return highest - lowest;
}

//+------------------------------------------------------------------+
//| 计算类RSI指标
//+------------------------------------------------------------------+
double CalculateTickRSI() {
    if(tick_index < LookbackPeriod) return 0.5;
    
    double gains = 0, losses = 0;
    int start_index = MathMax(0, tick_index - LookbackPeriod);
    
    for(int i = start_index; i < tick_index; i++) {
        int index = i % 100;
        double change = tick_history[index].price_change;
        
        if(change > 0) gains += change;
        else losses += MathAbs(change);
    }
    
    if(gains + losses == 0) return 0.5;
    return gains / (gains + losses);
}

//+------------------------------------------------------------------+
//| 成交量确认
//+------------------------------------------------------------------+
bool VolumeConfirmation() {
    if(tick_index < 10) return true;
    
    // 计算最近5个tick的平均成交量
    long recent_volume = 0, historical_volume = 0;
    
    for(int i = 0; i < 5; i++) {
        int index = (tick_index - 1 - i) % 100;
        recent_volume += tick_history[index].volume;
    }
    
    for(int i = 5; i < 10; i++) {
        int index = (tick_index - 1 - i) % 100;
        historical_volume += tick_history[index].volume;
    }
    
    // 最近成交量应该高于历史平均
    return (recent_volume > historical_volume * 1.2);
}

//+------------------------------------------------------------------+
//| 执行反转交易
//+------------------------------------------------------------------+
void ExecuteReversalTrade(ENUM_ORDER_TYPE order_type, MqlTick &tick, string reason) {
    double price = (order_type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
    double sl = 0, tp = 0;
    
    // 计算止损止盈
    if(order_type == ORDER_TYPE_BUY) {
        if(StopLoss > 0) sl = price - StopLoss * _Point;
        if(TakeProfit > 0) tp = price + TakeProfit * _Point;
    } else {
        if(StopLoss > 0) sl = price + StopLoss * _Point;
        if(TakeProfit > 0) tp = price - TakeProfit * _Point;
    }
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = order_type;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = StringFormat("%s|动量:%.2f", reason, momentum_strength);
    request.deviation = 3;
    
    if(OrderSend(request, result)) {
        hourly_trades++;
        PrintFormat("⚡ [反转] %s | 价格: %s | 动量: %.2f | 连续: %d | Ticket: %d", 
                   (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出",
                   DoubleToString(price, _Digits),
                   momentum_strength,
                   MathMax(consecutive_up, consecutive_down),
                   result.order);
    } else {
        PrintFormat("❌ 反转交易失败: %s (错误: %d)", reason, GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 风险检查
//+------------------------------------------------------------------+
bool RiskCheck() {
    // 检查每小时交易限制
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    datetime current_hour = dt.hour;
    
    if(current_hour != last_hour) {
        hourly_trades = 0;
        last_hour = current_hour;
    }
    
    if(hourly_trades >= MaxTradesPerHour) {
        static bool hourly_warned = false;
        if(!hourly_warned) {
            Print("⏰ 达到每小时交易限制: ", hourly_trades);
            hourly_warned = true;
        }
        return false;
    }
    
    // 检查日亏损
    if(daily_pnl < -MaxDailyLoss) {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 交易事件处理
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result) {
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        // 简化的盈亏计算
        if(trans.profit > 0) {
            successful_signals++;
        }
        daily_pnl += trans.profit;
        
        double success_rate = total_signals > 0 ? (double)successful_signals / total_signals * 100 : 0;
        PrintFormat("📊 反转统计 - 信号: %d | 成功率: %.1f%% | 日盈亏: $%.2f", 
                   total_signals, success_rate, daily_pnl);
    }
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    EventKillTimer();
    
    double success_rate = total_signals > 0 ? (double)successful_signals / total_signals * 100 : 0;
    
    Print("========================================");
    Print("⚡ Tick动量反转EA已停止");
    PrintFormat("🎯 总信号数: %d", total_signals);
    PrintFormat("✅ 成功率: %.1f%%", success_rate);
    PrintFormat("💰 日盈亏: $%.2f", daily_pnl);
    Print("========================================");
}