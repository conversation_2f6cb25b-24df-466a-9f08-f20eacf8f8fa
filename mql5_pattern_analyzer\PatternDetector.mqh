//+------------------------------------------------------------------+
//|                                               PatternDetector.mqh |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include "MathUtils.mqh"

//+------------------------------------------------------------------+
//| 形态检测器类 - 实现所有9种核心形态分析                                  |
//+------------------------------------------------------------------+
class CPatternDetector
{
private:
   int               m_minPatternBars;
   double            m_errorMargin;
   int               m_pullbackBars;
   
public:
                     CPatternDetector(void);
                    ~CPatternDetector(void);
   
   // 核心形态分析方法
   bool              AnalyzeTrendLineBreakout(const double &high[], const double &low[], const double &close[], int size, string &info);
   bool              AnalyzeSupportBoxBreakout(const double &close[], int size, string &info);
   bool              AnalyzeVolumeSurge(const double &open[], const double &close[], const long &volume[], int size, string &info);
   bool              AnalyzeConvergingTriangle(const double &high[], const double &low[], int size, string &info);
   bool              AnalyzeHeadShoulders(const double &low[], const long &volume[], int size, string &info);
   bool              AnalyzeKLinePattern(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size, string &info);
   bool              AnalyzePullbackEntry(const double &high[], const double &low[], const double &close[], const double &open[], int size, string &info);
   bool              AnalyzeDuckHead(const double &high[], const double &low[], int size, string &info);
   bool              AnalyzeCupHandle(const double &high[], const double &low[], int size, string &info);
   bool              AnalyzeBreakBottomReversal(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size, string &info);
   
   // 综合分析方法
   void              AnalyzeAllPatterns(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, bool &results[], string &infos[]);
   
   // 信号检测
   string            DetectTradingSignals(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size);
   
   // 设置参数
   void              SetMinPatternBars(int bars) { m_minPatternBars = bars; }
   void              SetErrorMargin(double margin) { m_errorMargin = margin; }
   void              SetPullbackBars(int bars) { m_pullbackBars = bars; }
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CPatternDetector::CPatternDetector(void)
{
   m_minPatternBars = 15;
   m_errorMargin = 0.001;
   m_pullbackBars = 3;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CPatternDetector::~CPatternDetector(void)
{
}

//+------------------------------------------------------------------+
//| 趋势线突破分析                                                     |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeTrendLineBreakout(const double &high[], const double &low[], const double &close[], int size, string &info)
{
   if(size < 20)
   {
      info = "数据不足";
      return false;
   }
   
   // 使用最近20根K线
   int recent_size = MathMin(20, size);
   double recent_lows[], x_values[];
   ArrayResize(recent_lows, recent_size);
   ArrayResize(x_values, recent_size);
   
   // 复制最近的低点数据
   for(int i = 0; i < recent_size; i++)
   {
      recent_lows[i] = low[size - recent_size + i];
      x_values[i] = i;
   }
   
   // 使用Theil-Sen回归拟合趋势线
   double slope, intercept, r_squared;
   CMathUtils::CalculateTheilSenRegression(x_values, recent_lows, recent_size, slope, intercept, r_squared);
   
   double current_price = close[size - 1];
   double trend_value = slope * (recent_size - 1) + intercept;
   
   // 判断突破条件
   bool is_breakout = (
      current_price > trend_value &&  // 价格在趋势线上方
      slope > 0 &&                    // 趋势向上
      r_squared > -0.5                // R方值合理
   );
   
   info = StringFormat("收盘价: %.8f, 趋势线值: %.8f, 斜率: %.8f, R²: %.4f", 
                       current_price, trend_value, slope, r_squared);
   
   return is_breakout;
}

//+------------------------------------------------------------------+
//| 底部箱体突破分析                                                   |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeSupportBoxBreakout(const double &close[], int size, string &info)
{
   if(size < 80)
   {
      info = "数据不足";
      return false;
   }
   
   // 使用最近80根K线
   int recent_size = MathMin(80, size);
   double recent_closes[];
   ArrayResize(recent_closes, recent_size);
   
   for(int i = 0; i < recent_size; i++)
   {
      recent_closes[i] = close[size - recent_size + i];
   }
   
   // 使用KDE识别价格密集区域
   double min_price = recent_closes[0], max_price = recent_closes[0];
   for(int i = 1; i < recent_size; i++)
   {
      if(recent_closes[i] < min_price) min_price = recent_closes[i];
      if(recent_closes[i] > max_price) max_price = recent_closes[i];
   }
   
   // 创建价格网格
   int grid_size = 50;
   double price_grid[], density[];
   ArrayResize(price_grid, grid_size);
   ArrayResize(density, grid_size);
   
   for(int i = 0; i < grid_size; i++)
   {
      price_grid[i] = min_price + (max_price - min_price) * i / (grid_size - 1);
   }
   
   // 计算KDE密度
   CMathUtils::CalculateKDEArray(recent_closes, recent_size, price_grid, grid_size, density);
   
   // 找到高密度区域
   double percentile_70 = CMathUtils::CalculatePercentile(density, grid_size, 0.7);
   double box_bottom = max_price, box_top = min_price;
   
   for(int i = 0; i < grid_size; i++)
   {
      if(density[i] > percentile_70)
      {
         if(price_grid[i] < box_bottom) box_bottom = price_grid[i];
         if(price_grid[i] > box_top) box_top = price_grid[i];
      }
   }
   
   // 计算触点数量
   int touch_points = 0;
   for(int i = 0; i < recent_size; i++)
   {
      if(recent_closes[i] >= box_bottom * (1 - m_errorMargin) && 
         recent_closes[i] <= box_top * (1 + m_errorMargin))
      {
         touch_points++;
      }
   }
   
   double latest_close = close[size - 1];
   bool result = latest_close > box_top * (1 + m_errorMargin) && touch_points >= 3;
   
   info = StringFormat("箱体范围: %.8f - %.8f, 触点数量: %d", box_bottom, box_top, touch_points);
   
   return result;
}

//+------------------------------------------------------------------+
//| 成交量放大分析                                                     |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeVolumeSurge(const double &open[], const double &close[], const long &volume[], int size, string &info)
{
   if(size < 6)
   {
      info = "数据不足";
      return false;
   }
   
   // 检查最新K线是否为阳线
   double latest_close = close[size - 1];
   double latest_open = open[size - 1];
   if(latest_close <= latest_open)
   {
      info = "非阳线";
      return false;
   }
   
   // 计算成交量变化
   long latest_volume = volume[size - 1];
   double sum_previous = 0;
   for(int i = size - 6; i < size - 1; i++)
   {
      sum_previous += (double)volume[i];
   }
   double average_previous_volume = sum_previous / 5.0;
   
   bool result = (double)latest_volume > 1.5 * average_previous_volume;
   double volume_ratio = (double)latest_volume / average_previous_volume;
   
   info = StringFormat("当前成交量: %I64d, 平均成交量: %.2f, 放大倍数: %.2f", 
                       latest_volume, average_previous_volume, volume_ratio);
   
   return result;
}

//+------------------------------------------------------------------+
//| 综合分析所有形态                                                   |
//+------------------------------------------------------------------+
void CPatternDetector::AnalyzeAllPatterns(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size, bool &results[], string &infos[])
{
   ArrayResize(results, 10);
   ArrayResize(infos, 10);
   
   results[0] = AnalyzeTrendLineBreakout(high, low, close, size, infos[0]);
   results[1] = AnalyzeSupportBoxBreakout(close, size, infos[1]);
   results[2] = AnalyzeVolumeSurge(open, close, volume, size, infos[2]);
   results[3] = AnalyzeConvergingTriangle(high, low, size, infos[3]);
   results[4] = AnalyzeHeadShoulders(low, volume, size, infos[4]);
   results[5] = AnalyzeKLinePattern(open, close, high, low, volume, size, infos[5]);
   results[6] = AnalyzePullbackEntry(high, low, close, open, size, infos[6]);
   results[7] = AnalyzeDuckHead(high, low, size, infos[7]);
   results[8] = AnalyzeCupHandle(high, low, size, infos[8]);
   results[9] = AnalyzeBreakBottomReversal(open, close, high, low, volume, size, infos[9]);
}

//+------------------------------------------------------------------+
//| 检测交易信号                                                       |
//+------------------------------------------------------------------+
string CPatternDetector::DetectTradingSignals(const double &open[], const double &high[], const double &low[], const double &close[], const long &volume[], int size)
{
   bool results[10];
   string infos[10];
   
   AnalyzeAllPatterns(open, high, low, close, volume, size, results, infos);
   
   // 提取各形态结果
   bool trend_break = results[0];
   bool box_break = results[1];
   bool volume_surge = results[2];
   bool triangle = results[3];
   bool head_shoulders = results[4];
   bool k_line = results[5];
   bool pullback = results[6];
   bool duck_head = results[7];
   bool cup = results[8];
   bool break_bottom = results[9];
   
   string signal_type = "";
   string signal_strength = "";
   string signal_details = "";
   
   // 1. 完美形态：趋势突破 + 箱体突破 + (三角形或头肩底) + K线组合 + 放量
   if(trend_break && (box_break || break_bottom) && (triangle || head_shoulders) && k_line && volume_surge)
   {
      signal_type = "A";
      signal_strength = "HIGH";
      signal_details = "完美形态：趋势突破 + 箱体突破 + 形态确认 + K线组合 + 放量";
   }
   // 2. 趋势突破组合：趋势突破 + 任意一个确认信号
   else if(trend_break && (box_break || triangle || head_shoulders || k_line || volume_surge || break_bottom))
   {
      signal_type = "B";
      signal_strength = "MEDIUM";
      signal_details = "趋势突破组合：趋势突破 + 确认信号";
   }
   // 3. 形态突破确认：(箱体/三角形/头肩底) + (K线/放量)确认
   else if((box_break || triangle || head_shoulders || break_bottom) && (k_line || volume_surge))
   {
      signal_type = "C";
      signal_strength = "MEDIUM";
      signal_details = "形态突破确认：形态 + 确认信号";
   }
   // 4. 其他交易机会：回调/老鸭头/茶杯带柄
   else if((pullback || duck_head || cup) && (k_line || volume_surge))
   {
      signal_type = "D";
      signal_strength = "MEDIUM";
      signal_details = "其他交易机会：回调/老鸭头/茶杯带柄 + 确认信号";
   }
   
   if(signal_type != "")
   {
      string result = StringFormat("信号类型: %s, 强度: %s\n详情: %s\n", signal_type, signal_strength, signal_details);
      
      // 添加满足的形态信息
      string pattern_names[10] = {"趋势线突破", "底部箱体突破", "放量", "收敛三角形", "头肩底", "K线组合", "回调买点", "老鸭头", "茶杯带柄", "破底翻"};
      
      result += "满足的形态:\n";
      for(int i = 0; i < 10; i++)
      {
         if(results[i])
         {
            result += StringFormat("%s: %s\n", pattern_names[i], infos[i]);
         }
      }
      
      return result;
   }
   
   return "无交易信号";
}

//+------------------------------------------------------------------+
//| 分析收敛三角形形态                                                   |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeConvergingTriangle(const double &high[], const double &low[], int size, string &info)
{
   if(size < 20)
   {
      info = "数据不足，无法分析收敛三角形";
      return false;
   }
   
   // 寻找高点和低点
   double recent_highs[5];
   double recent_lows[5];
   int high_count = 0, low_count = 0;
   
   // 找最近的高点和低点
   for(int i = size - 15; i < size - 2; i++)
   {
      // 检查是否为局部高点
      if(i > 1 && i < size - 2 && 
         high[i] > high[i-1] && high[i] > high[i+1] && 
         high[i] > high[i-2] && high[i] > high[i+2])
      {
         if(high_count < 5)
         {
            recent_highs[high_count] = high[i];
            high_count++;
         }
      }
      
      // 检查是否为局部低点
      if(i > 1 && i < size - 2 && 
         low[i] < low[i-1] && low[i] < low[i+1] && 
         low[i] < low[i-2] && low[i] < low[i+2])
      {
         if(low_count < 5)
         {
            recent_lows[low_count] = low[i];
            low_count++;
         }
      }
   }
   
   if(high_count < 3 || low_count < 3)
   {
      info = "高低点不足，无法形成三角形";
      return false;
   }
   
   // 检查高点是否呈下降趋势
   bool highs_descending = true;
   for(int i = 1; i < high_count; i++)
   {
      if(recent_highs[i] >= recent_highs[i-1])
      {
         highs_descending = false;
         break;
      }
   }
   
   // 检查低点是否呈上升趋势
   bool lows_ascending = true;
   for(int i = 1; i < low_count; i++)
   {
      if(recent_lows[i] <= recent_lows[i-1])
      {
         lows_ascending = false;
         break;
      }
   }
   
   bool is_converging = highs_descending && lows_ascending;
   
   if(is_converging)
   {
      double range_start = recent_highs[0] - recent_lows[0];
      double range_end = recent_highs[high_count-1] - recent_lows[low_count-1];
      double convergence_ratio = range_end / range_start;
      
      info = StringFormat("收敛三角形: 高点下降, 低点上升, 收敛比例: %.2f", convergence_ratio);
      return convergence_ratio < 0.6; // 收敛程度足够
   }
   
   info = "未检测到收敛三角形形态";
   return false;
}

//+------------------------------------------------------------------+
//| 分析头肩底形态                                                     |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeHeadShoulders(const double &low[], const long &volume[], int size, string &info)
{
   if(size < 30)
   {
      info = "数据不足，无法分析头肩底";
      return false;
   }
   
   // 寻找三个关键低点：左肩、头部、右肩
   double key_lows[3];
   int low_positions[3];
   int found_lows = 0;
   
   // 在最近30根K线中寻找局部低点
   for(int i = size - 25; i < size - 5 && found_lows < 3; i++)
   {
      // 检查是否为局部低点
      if(i > 2 && i < size - 3 && 
         low[i] < low[i-1] && low[i] < low[i+1] && 
         low[i] < low[i-2] && low[i] < low[i+2])
      {
         key_lows[found_lows] = low[i];
         low_positions[found_lows] = i;
         found_lows++;
      }
   }
   
   if(found_lows < 3)
   {
      info = "找到的低点不足3个";
      return false;
   }
   
   // 检查头肩底形态：中间的低点应该是最低的
   double left_shoulder = key_lows[0];
   double head = key_lows[1];
   double right_shoulder = key_lows[2];
   
   // 头部应该明显低于两肩
   bool valid_head = (head < left_shoulder * (1 - m_errorMargin)) && 
                     (head < right_shoulder * (1 - m_errorMargin));
   
   // 两肩高度应该相近
   double shoulder_diff = MathAbs(left_shoulder - right_shoulder) / left_shoulder;
   bool shoulders_similar = shoulder_diff < 0.05; // 5%误差范围
   
   // 检查成交量确认（头部成交量应该较大）
   bool volume_confirm = false;
   if(low_positions[1] < size)
   {
      double head_volume = (double)volume[low_positions[1]];
      double avg_volume = 0;
      for(int i = MathMax(0, low_positions[1] - 5); i <= MathMin(size-1, low_positions[1] + 5); i++)
      {
         avg_volume += (double)volume[i];
      }
      avg_volume /= 11.0;
      volume_confirm = head_volume > avg_volume * 1.2;
   }
   
   bool is_head_shoulders = valid_head && shoulders_similar && volume_confirm;
   
   info = StringFormat("头肩底: 左肩=%.5f, 头部=%.5f, 右肩=%.5f, 肩部差异=%.2f%%", 
                       left_shoulder, head, right_shoulder, shoulder_diff * 100);
   
   return is_head_shoulders;
}

//+------------------------------------------------------------------+
//| 分析K线组合形态                                                    |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeKLinePattern(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size, string &info)
{
   if(size < 3)
   {
      info = "数据不足";
      return false;
   }
   
   // 获取最近3根K线
   int idx = size - 1;
   double o1 = open[idx-2], h1 = high[idx-2], l1 = low[idx-2], c1 = close[idx-2];
   double o2 = open[idx-1], h2 = high[idx-1], l2 = low[idx-1], c2 = close[idx-1];
   double o3 = open[idx], h3 = high[idx], l3 = low[idx], c3 = close[idx];
   
   // 检查红三兵形态
   bool red_soldiers = (c1 > o1) && (c2 > o2) && (c3 > o3) && 
                       (c2 > c1) && (c3 > c2);
   
   // 检查早晨之星形态
   bool morning_star = (c1 < o1) && // 第一根阴线
                       (MathAbs(c2 - o2) < (h2 - l2) * 0.3) && // 第二根十字星
                       (c3 > o3) && (c3 > (o1 + c1) / 2); // 第三根阳线
   
   // 检查锤子线形态
   double body3 = MathAbs(c3 - o3);
   double lower_shadow3 = MathMin(o3, c3) - l3;
   double upper_shadow3 = h3 - MathMax(o3, c3);
   bool hammer = (lower_shadow3 > body3 * 2) && (upper_shadow3 < body3 * 0.5);
   
   if(red_soldiers)
   {
      info = "红三兵形态";
      return true;
   }
   else if(morning_star)
   {
      info = "早晨之星形态";
      return true;
   }
   else if(hammer)
   {
      info = "锤子线形态";
      return true;
   }
   
   info = "无明显K线组合形态";
   return false;
}

//+------------------------------------------------------------------+
//| 分析回调买点                                                       |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzePullbackEntry(const double &high[], const double &low[], const double &close[], const double &open[], int size, string &info)
{
   if(size < 20)
   {
      info = "数据不足";
      return false;
   }
   
   // 检查是否处于上升趋势
   double trend_slope, trend_intercept, r_squared;
   double x_values[20], close_values[20];
   
   for(int i = 0; i < 20; i++)
   {
      x_values[i] = i;
      close_values[i] = close[size - 20 + i];
   }
   
   CMathUtils::CalculateTheilSenRegression(x_values, close_values, 20, trend_slope, trend_intercept, r_squared);
   
   if(trend_slope <= 0)
   {
      info = "非上升趋势";
      return false;
   }
   
   // 检查最近是否有回调
   double recent_high = high[size-1];
   for(int i = size - 5; i < size; i++)
   {
      if(high[i] > recent_high) recent_high = high[i];
   }
   
   double current_close = close[size-1];
   double pullback_ratio = (recent_high - current_close) / recent_high;
   
   // 回调幅度在3%-8%之间认为是健康回调
   bool healthy_pullback = (pullback_ratio > 0.03) && (pullback_ratio < 0.08);
   
   // 检查是否有支撑
   bool has_support = false;
   for(int i = size - 15; i < size - 5; i++)
   {
      if(MathAbs(low[i] - current_close) / current_close < 0.02)
      {
         has_support = true;
         break;
      }
   }
   
   bool is_pullback_entry = healthy_pullback && has_support;
   
   info = StringFormat("回调幅度: %.2f%%, 趋势斜率: %.6f", pullback_ratio * 100, trend_slope);
   
   return is_pullback_entry;
}

//+------------------------------------------------------------------+
//| 分析老鸭头形态                                                     |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeDuckHead(const double &high[], const double &low[], int size, string &info)
{
   if(size < 30)
   {
      info = "数据不足";
      return false;
   }
   
   // 寻找鸭头（高点）
   double duck_head = 0;
   int head_position = -1;
   
   for(int i = size - 25; i < size - 10; i++)
   {
      if(i > 2 && i < size - 3)
      {
         if(high[i] > high[i-1] && high[i] > high[i+1] && 
            high[i] > high[i-2] && high[i] > high[i+2])
         {
            if(high[i] > duck_head)
            {
               duck_head = high[i];
               head_position = i;
            }
         }
      }
   }
   
   if(head_position == -1)
   {
      info = "未找到鸭头";
      return false;
   }
   
   // 检查鸭嘴（回调低点）
   double duck_mouth = high[size-1];
   for(int i = head_position + 1; i < size; i++)
   {
      if(low[i] < duck_mouth) duck_mouth = low[i];
   }
   
   // 检查当前价格是否突破鸭头
   double current_high = high[size-1];
   bool breakout = current_high > duck_head * (1 + m_errorMargin);
   
   // 检查回调幅度
   double pullback_ratio = (duck_head - duck_mouth) / duck_head;
   bool valid_pullback = (pullback_ratio > 0.05) && (pullback_ratio < 0.20);
   
   bool is_duck_head = breakout && valid_pullback;
   
   info = StringFormat("鸭头: %.5f, 鸭嘴: %.5f, 回调: %.2f%%", 
                       duck_head, duck_mouth, pullback_ratio * 100);
   
   return is_duck_head;
}

//+------------------------------------------------------------------+
//| 分析茶杯带柄形态                                                   |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeCupHandle(const double &high[], const double &low[], int size, string &info)
{
   if(size < 40)
   {
      info = "数据不足";
      return false;
   }
   
   // 寻找茶杯的左边缘（高点）
   double left_rim = 0;
   int left_position = -1;
   
   for(int i = size - 35; i < size - 25; i++)
   {
      if(high[i] > left_rim)
      {
         left_rim = high[i];
         left_position = i;
      }
   }
   
   // 寻找茶杯底部
   double cup_bottom = left_rim;
   for(int i = left_position; i < size - 10; i++)
   {
      if(low[i] < cup_bottom) cup_bottom = low[i];
   }
   
   // 寻找茶杯右边缘
   double right_rim = 0;
   for(int i = size - 15; i < size - 5; i++)
   {
      if(high[i] > right_rim) right_rim = high[i];
   }
   
   // 检查茶杯形状
   double cup_depth = (left_rim - cup_bottom) / left_rim;
   bool valid_cup = (cup_depth > 0.12) && (cup_depth < 0.35); // 12%-35%深度
   bool rims_similar = MathAbs(left_rim - right_rim) / left_rim < 0.05; // 边缘相似
   
   // 检查手柄（小幅回调）
   double handle_low = right_rim;
   for(int i = size - 10; i < size; i++)
   {
      if(low[i] < handle_low) handle_low = low[i];
   }
   
   double handle_depth = (right_rim - handle_low) / right_rim;
   bool valid_handle = (handle_depth > 0.02) && (handle_depth < 0.12); // 2%-12%回调
   
   // 检查突破
   double current_high = high[size-1];
   bool breakout = current_high > right_rim * (1 + m_errorMargin);
   
   bool is_cup_handle = valid_cup && rims_similar && valid_handle && breakout;
   
   info = StringFormat("茶杯深度: %.2f%%, 手柄深度: %.2f%%", 
                       cup_depth * 100, handle_depth * 100);
   
   return is_cup_handle;
}

//+------------------------------------------------------------------+
//| 分析破底翻形态                                                     |
//+------------------------------------------------------------------+
bool CPatternDetector::AnalyzeBreakBottomReversal(const double &open[], const double &close[], const double &high[], const double &low[], const long &volume[], int size, string &info)
{
   if(size < 20)
   {
      info = "数据不足";
      return false;
   }
   
   // 寻找前期重要低点
   double important_low = low[size-1];
   int low_position = size - 1;
   
   for(int i = size - 15; i < size - 3; i++)
   {
      if(low[i] < important_low)
      {
         important_low = low[i];
         low_position = i;
      }
   }
   
   // 检查是否有向下破位
   bool broke_below = false;
   int break_position = -1;
   
   for(int i = low_position + 1; i < size - 1; i++)
   {
      if(low[i] < important_low * (1 - m_errorMargin))
      {
         broke_below = true;
         break_position = i;
         break;
      }
   }
   
   if(!broke_below)
   {
      info = "未发生破底";
      return false;
   }
   
   // 检查破底后的反转
   double break_low = low[break_position];
   double current_close = close[size-1];
   bool reversal = current_close > important_low * (1 + m_errorMargin);
   
   // 检查成交量确认
   double break_volume = (double)volume[break_position];
   double recent_avg_volume = 0;
   for(int i = MathMax(0, break_position - 5); i < break_position; i++)
   {
      recent_avg_volume += (double)volume[i];
   }
   recent_avg_volume /= 5.0;
   
   bool volume_confirm = break_volume > recent_avg_volume * 1.5;
   
   bool is_break_bottom_reversal = reversal && volume_confirm;
   
   info = StringFormat("重要低点: %.5f, 破底价: %.5f, 当前价: %.5f", 
                       important_low, break_low, current_close);
   
   return is_break_bottom_reversal;
}
