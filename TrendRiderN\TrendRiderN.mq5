//+------------------------------------------------------------------+
//|                                                  TrendRiderN.mq5 |
//|                        Copyright 2024, TrendRider Development |
//|                                             https://mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TrendRider Development"
#property link      "https://mql5.com"
#property version   "2.00"
#property description "TrendRiderN v2.0 - 智能动态多时间框架趋势跟踪EA"
#property description "核心策略: 动态周期适应，看大做小，顺势而为"
#property description "特色: 自适应时间框架配置，模块化架构，完善风险管理"

//+------------------------------------------------------------------+
//| 包含重构后的模块化类库                                              |
//+------------------------------------------------------------------+
#include "TimeFrameManager.mqh"
#include "MarketAnalyzer.mqh"
#include "SignalManager.mqh"
#include "RiskManager.mqh"

//+------------------------------------------------------------------+
//| EA输入参数                                                        |
//+------------------------------------------------------------------+

//--- EA基础设置
input group "=== EA基础设置 ==="
input ulong  InpMagicNumber = 202402;           // EA魔术号(v2.0)
input string InpSymbols = "";                   // 交易品种列表(空=当前图表品种)

//--- 动态时间框架设置
input group "=== 动态时间框架配置 ==="
input ENUM_TIMEFRAMES InpBaseTimeframe = PERIOD_H4;     // 基础交易周期(自动计算趋势和止损周期)
input string InpTimeFrameInfo = "系统将自动计算: 趋势周期=基础周期×4, 止损周期=基础周期÷4"; // 信息说明

//--- 风险管理设置
input group "=== 风险管理配置 ==="
input double InpRiskNormal = 2.0;               // 正常单笔风险百分比 (%)
input double InpRiskReduced = 1.0;              // 连续亏损后降低的风险百分比 (%)
input int    InpLosingStreakThreshold = 3;      // 触发风险降低的连亏次数
input double InpMaxDailyLoss = 5.0;             // 最大日亏损百分比 (%)
input double InpMaxDrawdown = 10.0;             // 最大回撤百分比 (%)

//--- 加仓管理
input group "=== 加仓管理设置 ==="
input bool   InpEnablePyramiding = true;        // 启用盈利加仓
input int    InpMaxPositions = 3;               // 最大持仓数量
input double InpMinProfitForPyramid = 50.0;     // 加仓最小盈利金额

//--- 高级设置
input group "=== 高级设置 ==="
input double InpMinSignalConfidence = 0.6;      // 最小信号置信度(0-1)
input int    InpMAPeriod = 200;                 // 移动平均线周期

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
// 核心管理器
CTimeFrameManager*   g_tfManager;               // 时间框架管理器
CMarketAnalyzer*     g_marketAnalyzer;          // 市场分析器
CSignalManager*      g_signalManager;           // 信号管理器
CRiskManager*        g_riskManager;             // 风险管理器

// 交易工具
CTrade               g_trade;                   // 交易执行类
CPositionInfo        g_position;                // 持仓信息类

// 基础变量
string               g_currentSymbol = "";      // 当前交易品种
datetime             g_lastBarTime = 0;         // 最后K线时间
bool                 g_systemInitialized = false; // 系统初始化状态

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== TrendRiderN v2.0 EA 初始化开始 ===");
   
   // 设置当前交易品种
   g_currentSymbol = (InpSymbols == "") ? Symbol() : InpSymbols;
   
   // 创建时间框架管理器
   g_tfManager = new CTimeFrameManager();
   if(g_tfManager == NULL)
   {
      Print("错误: 无法创建时间框架管理器");
      return INIT_FAILED;
   }
   
   // 创建市场分析器
   g_marketAnalyzer = new CMarketAnalyzer(g_currentSymbol, InpBaseTimeframe);
   if(g_marketAnalyzer == NULL)
   {
      Print("错误: 无法创建市场分析器");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 初始化市场分析器
   if(!g_marketAnalyzer.Initialize())
   {
      Print("错误: 市场分析器初始化失败");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 创建信号管理器
   g_signalManager = new CSignalManager(g_currentSymbol, g_marketAnalyzer);
   if(g_signalManager == NULL)
   {
      Print("错误: 无法创建信号管理器");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 初始化信号管理器
   if(!g_signalManager.Initialize())
   {
      Print("错误: 信号管理器初始化失败");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 创建风险管理器
   g_riskManager = new CRiskManager(g_currentSymbol, InpMagicNumber);
   if(g_riskManager == NULL)
   {
      Print("错误: 无法创建风险管理器");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 配置风险管理器
   RiskConfig risk_config;
   risk_config.normal_risk_percent = InpRiskNormal;
   risk_config.reduced_risk_percent = InpRiskReduced;
   risk_config.losing_streak_threshold = InpLosingStreakThreshold;
   risk_config.max_daily_loss_percent = InpMaxDailyLoss;
   risk_config.max_drawdown_percent = InpMaxDrawdown;
   risk_config.max_positions = InpMaxPositions;
   risk_config.min_profit_for_pyramid = InpMinProfitForPyramid;
   risk_config.enable_pyramiding = InpEnablePyramiding;
   
   g_riskManager.SetRiskConfig(risk_config);
   
   // 初始化风险管理器
   if(!g_riskManager.Initialize())
   {
      Print("错误: 风险管理器初始化失败");
      CleanupOnExit();
      return INIT_FAILED;
   }
   
   // 设置交易参数
   g_trade.SetExpertMagicNumber(InpMagicNumber);
   g_trade.SetMarginMode();
   g_trade.SetTypeFillingBySymbol(g_currentSymbol);
   
   // 设置系统状态
   g_systemInitialized = true;
   
   // 启动定时器
   EventSetTimer(60);
   
   // 显示初始化信息
   PrintSystemStatus();
   
   Print("=== TrendRiderN v2.0 EA 初始化完成 ===");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== TrendRiderN v2.0 EA 反初始化 ===");
   Print("反初始化原因: ", reason);
   
   // 停止定时器
   EventKillTimer();
   
   // 清理资源
   CleanupOnExit();
   
   Print("=== TrendRiderN v2.0 EA 反初始化完成 ===");
}

//+------------------------------------------------------------------+
//| Timer function - EA主循环                                        |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(!g_systemInitialized)
      return;
   
   // 检查是否为新的交易周期K线
   TimeFrameConfig tf_config = g_marketAnalyzer.GetTimeFrameConfig();
   if(!IsNewBar(tf_config.base_timeframe))
      return;
   
   // 检查是否允许交易
   if(!g_riskManager.IsTradingAllowed())
   {
      Comment("TrendRiderN v2.0: 交易被禁用 - 风险管理限制");
      return;
   }
   
   // 执行主交易逻辑
   ProcessMainTradingLogic();
}

//+------------------------------------------------------------------+
//| 处理主交易逻辑                                                    |
//+------------------------------------------------------------------+
void ProcessMainTradingLogic()
{
   // 1. 执行市场分析
   MarketAnalysisResult market_analysis = g_marketAnalyzer.AnalyzeMarket();
   if(!market_analysis.is_valid)
   {
      Comment("TrendRiderN v2.0: 市场分析无效");
      return;
   }
   
   string trend_text = (market_analysis.trend_direction == 1) ? "多头" : 
                      (market_analysis.trend_direction == -1) ? "空头" : "无趋势";
   
   // 2. 检查当前持仓情况
   int current_positions = g_riskManager.GetCurrentPositionCount();
   
   if(current_positions > 0)
   {
      // 有持仓时，检查加仓机会
      if(g_riskManager.IsPyramidingAllowed())
      {
         ProcessPyramidingLogic(market_analysis);
      }
      else
      {
         Comment("TrendRiderN v2.0: 趋势=", trend_text, " | 持仓=", current_positions, " | 等待加仓条件");
      }
   }
   else
   {
      // 无持仓时，寻找新交易机会
      ProcessNewTradeLogic(market_analysis);
   }
   
   // 更新显示信息
   UpdateDisplayInfo(market_analysis);
}

//+------------------------------------------------------------------+
//| 处理新交易逻辑                                                    |
//+------------------------------------------------------------------+
void ProcessNewTradeLogic(const MarketAnalysisResult &market_analysis)
{
   // 生成交易信号
   TradingSignal signal = g_signalManager.GenerateSignal();
   
   if(!signal.is_valid)
   {
      Comment("TrendRiderN v2.0: ", signal.description);
      return;
   }
   
   // 检查信号置信度
   if(signal.confidence < InpMinSignalConfidence)
   {
      Comment("TrendRiderN v2.0: 信号置信度低于阈值 (", 
              DoubleToString(signal.confidence * 100, 1), "% < ", 
              DoubleToString(InpMinSignalConfidence * 100, 1), "%)");
      return;
   }
   
   // 执行入场交易
   ExecuteNewTrade(signal);
}

//+------------------------------------------------------------------+
//| 处理加仓逻辑                                                      |
//+------------------------------------------------------------------+
void ProcessPyramidingLogic(const MarketAnalysisResult &market_analysis)
{
   // 生成加仓信号
   TradingSignal signal = g_signalManager.GenerateSignal();
   
   if(!signal.is_valid)
   {
      return;
   }
   
   // 加仓信号需要更高置信度
   double pyramid_confidence_threshold = InpMinSignalConfidence + 0.1;
   if(signal.confidence < pyramid_confidence_threshold)
   {
      return;
   }
   
   // 执行加仓交易
   ExecutePyramidTrade(signal);
}

//+------------------------------------------------------------------+
//| 执行新交易                                                        |
//+------------------------------------------------------------------+
void ExecuteNewTrade(const TradingSignal &signal)
{
   // 计算仓位大小
   PositionSizeResult pos_result = g_riskManager.CalculatePositionSize(signal);
   if(!pos_result.is_valid)
   {
      Print("错误: 仓位计算失败 - ", pos_result.calculation_info);
      return;
   }
   
   // 执行开仓
   bool result = false;
   string comment = StringFormat("TrendRiderN v2.0 %s [C:%.1f%%]", 
                               (signal.signal_type == SIGNAL_BUY) ? "Buy" : "Sell",
                               signal.confidence * 100);
   
   if(signal.signal_type == SIGNAL_BUY)
   {
      result = g_trade.Buy(pos_result.lot_size, g_currentSymbol, 
                          signal.entry_price, signal.stop_loss, signal.take_profit, comment);
   }
   else
   {
      result = g_trade.Sell(pos_result.lot_size, g_currentSymbol, 
                           signal.entry_price, signal.stop_loss, signal.take_profit, comment);
   }
   
   // 输出结果
   if(result)
   {
      Print("新交易开仓成功:");
      Print(signal.description);
      Print(pos_result.calculation_info);
   }
   else
   {
      Print("开仓失败: ", g_trade.ResultRetcodeDescription());
      Print("信号信息: ", signal.description);
   }
}

//+------------------------------------------------------------------+
//| 执行加仓交易                                                      |
//+------------------------------------------------------------------+
void ExecutePyramidTrade(const TradingSignal &signal)
{
   // 使用降低的风险计算加仓仓位
   TradingSignal pyramid_signal = signal;
   
   // 计算加仓仓位(使用正常风险的一半)
   PositionSizeResult pos_result = g_riskManager.CalculatePositionSize(pyramid_signal);
   if(!pos_result.is_valid)
   {
      Print("加仓仓位计算失败: ", pos_result.calculation_info);
      return;
   }
   
   // 加仓使用较小的仓位
   double pyramid_lot_size = pos_result.lot_size * 0.5;
   
   // 执行加仓
   bool result = false;
   string comment = StringFormat("TrendRiderN v2.0 Pyramid %s [C:%.1f%%]", 
                               (signal.signal_type == SIGNAL_BUY) ? "Buy" : "Sell",
                               signal.confidence * 100);
   
   if(signal.signal_type == SIGNAL_BUY)
   {
      result = g_trade.Buy(pyramid_lot_size, g_currentSymbol, 
                          signal.entry_price, signal.stop_loss, 0, comment);
   }
   else
   {
      result = g_trade.Sell(pyramid_lot_size, g_currentSymbol, 
                           signal.entry_price, signal.stop_loss, 0, comment);
   }
   
   if(result)
   {
      Print("加仓成功:");
      Print("方向: ", (signal.signal_type == SIGNAL_BUY) ? "买入" : "卖出");
      Print("手数: ", pyramid_lot_size);
      Print("置信度: ", DoubleToString(signal.confidence * 100, 1), "%");
      
      // 更新所有持仓止损
      UpdateAllPositionStopLosses(signal);
   }
   else
   {
      Print("加仓失败: ", g_trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| 更新所有持仓止损                                                |
//+------------------------------------------------------------------+
void UpdateAllPositionStopLosses(const TradingSignal &signal)
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(g_position.SelectByIndex(i))
      {
         if(g_position.Symbol() == g_currentSymbol && 
            g_position.Magic() == InpMagicNumber)
         {
            double current_sl = g_position.StopLoss();
            double new_sl = signal.stop_loss;
            bool should_update = false;
            
            if(g_position.PositionType() == POSITION_TYPE_BUY)
            {
               // 多头持仓：只有新止损更高时才更新
               should_update = (new_sl > current_sl);
            }
            else
            {
               // 空头持仓：只有新止损更低时才更新
               should_update = (new_sl < current_sl || current_sl == 0.0);
            }
            
            if(should_update)
            {
               if(g_trade.PositionModify(g_position.Ticket(), new_sl, g_position.TakeProfit()))
               {
                  Print("止损更新成功: 订单", g_position.Ticket(), ", 新止损:", new_sl);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!g_systemInitialized)
      return;
   
   static datetime last_update_time = 0;
   datetime current_time = TimeCurrent();
   
   // 每30秒更新一次显示信息
   if(current_time - last_update_time >= 30)
   {
      UpdateQuickDisplayInfo();
      last_update_time = current_time;
   }
}

//+------------------------------------------------------------------+
//| Trade transaction function                                        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   if(!g_systemInitialized)
      return;
   
   // 只处理成交事件
   if(trans.type != TRADE_TRANSACTION_DEAL_ADD)
      return;
   
   // 只处理本 EA的交易
   if(trans.symbol != g_currentSymbol)
      return;
   
   // 获取成交信息
   CDealInfo deal;
   if(!deal.SelectByIndex(trans.deal))
      return;
   
   if(deal.Magic() != InpMagicNumber)
      return;
   
   // 处理平仓事件
   if(deal.Entry() == DEAL_ENTRY_OUT)
   {
      double profit = deal.Profit();
      bool is_winning = (profit >= 0);
      
      // 更新风险管理器
      g_riskManager.UpdateTradeResult(profit, is_winning);
      
      // 输出交易结果
      Print("交易完成: ", deal.Symbol(), ", 盈亏:", profit, ", 类型:", is_winning ? "盈利" : "亏损");
   }
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                   |
//+------------------------------------------------------------------+
bool IsNewBar(ENUM_TIMEFRAMES timeframe)
{
   static datetime last_time = 0;
   datetime current_time = iTime(g_currentSymbol, timeframe, 0);
   
   if(current_time != last_time)
   {
      last_time = current_time;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 清理资源                                                          |
//+------------------------------------------------------------------+
void CleanupOnExit()
{
   if(g_riskManager != NULL)
   {
      delete g_riskManager;
      g_riskManager = NULL;
   }
   
   if(g_signalManager != NULL)
   {
      delete g_signalManager;
      g_signalManager = NULL;
   }
   
   if(g_marketAnalyzer != NULL)
   {
      delete g_marketAnalyzer;
      g_marketAnalyzer = NULL;
   }
   
   if(g_tfManager != NULL)
   {
      delete g_tfManager;
      g_tfManager = NULL;
   }
   
   g_systemInitialized = false;
}

//+------------------------------------------------------------------+
//| 显示系统状态                                                      |
//+------------------------------------------------------------------+
void PrintSystemStatus()
{
   TimeFrameConfig tf_config = g_marketAnalyzer.GetTimeFrameConfig();
   
   Print("=== 系统配置信息 ===");
   Print("交易品种: ", g_currentSymbol);
   Print("魔术号: ", InpMagicNumber);
   Print(g_tfManager.GetTimeFrameDescription(tf_config));
   Print("最小信号置信度: ", DoubleToString(InpMinSignalConfidence * 100, 1), "%");
   Print(g_riskManager.GetRiskManagerStatus());
}

//+------------------------------------------------------------------+
//| 更新显示信息                                                      |
//+------------------------------------------------------------------+
void UpdateDisplayInfo(const MarketAnalysisResult &market_analysis)
{
   string trend_text = (market_analysis.trend_direction == 1) ? "多头" : 
                      (market_analysis.trend_direction == -1) ? "空头" : "无趋势";
   
   TimeFrameConfig tf_config = g_marketAnalyzer.GetTimeFrameConfig();
   
   string comment = "=== TrendRiderN v2.0 ===\n";
   comment += "品种: " + g_currentSymbol + "\n";
   comment += g_tfManager.GetTimeFrameDescription(tf_config) + "\n";
   comment += "趋势: " + trend_text + " (强度:" + DoubleToString(market_analysis.trend_strength * 100, 1) + "%)\n";
   comment += "持仓: " + IntegerToString(g_riskManager.GetCurrentPositionCount()) + "/" + IntegerToString(InpMaxPositions) + "\n";
   comment += "组合风险: " + DoubleToString(g_riskManager.GetPortfolioRisk(), 2) + "%\n";
   comment += "状态: " + (g_riskManager.IsTradingAllowed() ? "正常" : "禁用") + "\n";
   
   Comment(comment);
}

//+------------------------------------------------------------------+
//| 快速更新显示信息                                                |\n
//+------------------------------------------------------------------+
void UpdateQuickDisplayInfo()
{
   if(!g_systemInitialized)
      return;
   
   string quick_info = "TrendRiderN v2.0 | ";
   quick_info += g_currentSymbol + " | ";
   quick_info += "持仓:" + IntegerToString(g_riskManager.GetCurrentPositionCount()) + " | ";
   quick_info += "状态:" + (g_riskManager.IsTradingAllowed() ? "✓" : "✗");
   
   Comment(quick_info);
}