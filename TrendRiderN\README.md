# TrendRiderN v2.0 - 智能动态多时间框架趋势跟踪系统

## 项目概述
TrendRiderN v2.0 是基于"动态周期适应，看大做小，顺势而为"交易哲学的智能EA系统。该版本采用完全重构的模块化架构，实现了自适应时间框架配置和完善的风险管理体系。

## 核心特性

### 🎯 动态时间框架适应
- **自适应配置**: 基于基础周期自动计算趋势和止损周期
- **灵活比例**: 趋势周期 = 基础周期 × 4倍，止损周期 = 基础周期 ÷ 4倍
- **智能映射**: 5m做单→1m止损,15m趋势；日线做单→4h止损,W1趋势
- **特殊处理**: 针对极端周期（M1、D1、W1）的特殊优化

### 🏗️ 模块化架构
- **TimeFrameManager**: 动态时间框架管理
- **MarketAnalyzer**: 多维度市场分析
- **SignalManager**: 智能交易信号生成
- **RiskManager**: 全面风险控制系统

### 📊 智能市场分析
- **多时间框架联合分析**: 三个时间框架协同工作
- **N字结构识别**: 高精度趋势结构识别算法
- **趋势强度量化**: 0-1标准化趋势强度评估
- **支撑阻力位计算**: 动态关键价位识别

### 🎯 精准信号系统
- **置信度评估**: 多维度信号质量评分(0-1)
- **入场条件验证**: 吞没形态、突破确认等多重验证
- **信号强度分级**: 弱/中/强三级信号分类
- **风险回报比优化**: 智能止盈止损计算

### 🛡️ 全面风险管理
- **动态仓位计算**: 基于风险百分比的智能仓位sizing
- **连亏保护机制**: 自动降低风险后续交易
- **日风险限制**: 日亏损和回撤双重保护
- **组合风险监控**: 实时总体风险评估

### 📈 智能加仓系统
- **盈利加仓**: 仅在盈利状态下考虑加仓
- **渐进式加仓**: 降低后续仓位规模
- **动态止损跟进**: 所有持仓统一止损管理
- **置信度门槛**: 加仓信号需要更高置信度

## 文件结构
```
TrendRiderN/
├── TrendRiderN.mq5           # 主EA文件 - 重构版本
├── TimeFrameManager.mqh      # 时间框架管理器
├── MarketAnalyzer.mqh        # 市场分析器
├── SignalManager.mqh         # 信号管理器
├── RiskManager.mqh           # 风险管理器
└── README.md                 # 项目说明文档
```

## 核心参数配置

### 基础设置
- `InpMagicNumber`: EA魔术号 (202402) - v2.0标识
- `InpSymbols`: 交易品种(空=当前图表品种)

### 动态时间框架
- `InpBaseTimeframe`: 基础交易周期(PERIOD_H4)
- 系统自动计算: 趋势周期=基础×4，止损周期=基础÷4

### 风险管理
- `InpRiskNormal`: 正常风险 (2.0%)
- `InpRiskReduced`: 连亏后降低风险 (1.0%)
- `InpLosingStreakThreshold`: 连亏阈值 (3次)
- `InpMaxDailyLoss`: 最大日亏损 (5.0%)
- `InpMaxDrawdown`: 最大回撤 (10.0%)

### 加仓管理
- `InpEnablePyramiding`: 启用加仓 (true)
- `InpMaxPositions`: 最大持仓数 (3)
- `InpMinProfitForPyramid`: 加仓最小盈利 (50.0)

### 高级设置
- `InpMinSignalConfidence`: 最小信号置信度 (0.6)
- `InpMAPeriod`: MA周期 (200)

## 安装使用

### 环境要求
- MetaTrader 5 平台
- MQL5 编译器

### 安装步骤
1. 将所有 `.mqh` 文件复制到 `MQL5/Include/TrendRiderN/` 文件夹
2. 将 `TrendRiderN.mq5` 复制到 `MQL5/Experts/` 文件夹
3. 重启MT5并编译EA
4. 在图表上附加EA，配置参数

### 使用建议
1. **测试环境**: 建议先在模拟账户测试
2. **周期选择**: 根据交易风格选择合适的基础周期
3. **风险设置**: 根据账户大小调整风险参数
4. **信号阈值**: 新手建议提高最小置信度到0.7

## 动态周期适应示例

| 基础周期 | 趋势周期 | 止损周期 | 应用场景 |
|---------|---------|---------|---------|
| M5      | M15     | M1      | 短线交易 |
| M15     | H1      | M3      | 日内交易 |
| H1      | H4      | M15     | 波段交易 |
| H4      | D1      | H1      | 中期交易 |
| D1      | W1      | H4      | 长期投资 |

## 技术特点

### 架构优势
- **高度解耦**: 各模块独立，便于维护和扩展
- **错误处理**: 完善的异常处理和资源管理
- **性能优化**: 智能缓存和按需计算
- **扩展性强**: 易于添加新的分析模块和策略

### 风险控制
- **多层防护**: 信号质量、仓位管理、日风险三重保护
- **智能适应**: 根据表现自动调整风险水平
- **实时监控**: 持续监控组合风险状态
- **紧急制动**: 触发限制时自动停止交易

### 代码质量
- **现代MQL5**: 使用最新MQL5特性和最佳实践
- **清晰注释**: 详细的代码注释和文档
- **模块化设计**: 单一职责原则，高内聚低耦合
- **易于测试**: 各模块可独立测试和验证

## 版本历史

### v2.0 (当前版本)
- ✅ 完全重构为模块化架构
- ✅ 实现动态时间框架适应机制
- ✅ 全新的风险管理系统
- ✅ 智能信号置信度评估
- ✅ 完善的加仓管理策略
- ✅ 代码逻辑完全闭环

### v1.0 (原版本)
- 基础N字结构识别
- 固定时间框架配置(D1/H4/H1)
- 简单风险管理
- 单文件架构

## 风险提示
⚠️ **重要声明**: 本系统仅供学习和研究使用，不构成投资建议。外汇交易存在高风险，可能导致资金损失。使用前请：

1. 在模拟环境充分测试
2. 充分理解系统工作原理
3. 根据自身情况调整参数
4. 严格控制风险敞口
5. 定期监控系统运行状态

---
**版本**: v2.00 | **开发**: TrendRider Team | **版权**: 2024 | **许可**: 仅限学习研究使用