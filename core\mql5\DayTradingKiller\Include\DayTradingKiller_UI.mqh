//+------------------------------------------------------------------+
//|                                    DayTradingKiller_UI.mqh      |
//|                                    日内交易大杀器 - UI界面类      |
//|                                    Copyright 2025, Your Company |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

#include "DayTradingKiller_Core.mqh"

//--- UI常量
#define MAIN_PANEL_WIDTH 350
#define MAIN_PANEL_HEIGHT 600
#define BUTTON_WIDTH 80
#define BUTTON_HEIGHT 25
#define MARGIN 10
#define LINE_HEIGHT 20

//--- 前置声明结构体
struct SPsychologyState
{
    int state;
    string warning_message;
    string status_text;
    color status_color;
};

struct SVolatilityAnalysis
{
    string current_session;
    double session_volatility;
    double daily_range;
    double remaining_range;
    double completion_percent;
    string time_info;
};

struct SVegasAnalysis
{
    string trend_direction;
    string signal_strength;
    bool is_trending;
    double ema_alignment_score;
    string market_phase;
    color trend_color;
};

//+------------------------------------------------------------------+
//| UI管理类                                                          |
//+------------------------------------------------------------------+
class CDayTradingUI
{
private:
    ENUM_BASE_CORNER m_corner;
    color m_bg_color;
    color m_active_color;
    color m_inactive_color;
    color m_text_color;
    
    int m_panel_x;
    int m_panel_y;
    
public:
    CDayTradingUI();
    ~CDayTradingUI();
    
    bool Initialize(ENUM_BASE_CORNER corner, color bg_color, color active_color, 
                   color inactive_color, color text_color);
    void CreateMainPanel();
    void DestroyAllPanels();
    void UpdateMainDisplay(const STradeCalculation &calc, const SPsychologyState &psych, 
                          const SVolatilityAnalysis &vol, const SVegasAnalysis &vegas);
    void UpdateRiskButtons(ENUM_RISK_MODE active_mode);
    
private:
    void CreateBackground();
    void CreateTitleSection();
    void CreateRiskButtons();
    void CreateTradeButtons();
    void CreateInfoLabels();
    void CreatePsychologySection();
    void CreateVolatilitySection();
    void CreateVegasSection();
    void SetObjectPosition(string name, int x, int y, int width = 0, int height = 0);
    void CreateButton(string name, string text, int x, int y, int width, int height, color bg_color);
    void CreateLabel(string name, string text, int x, int y, color text_color, int font_size = 9);
    void CreateSectionHeader(string name, string text, int x, int y, color header_color);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CDayTradingUI::CDayTradingUI()
{
    m_corner = CORNER_LEFT_UPPER;
    m_bg_color = clrDarkSlateGray;
    m_active_color = clrLimeGreen;
    m_inactive_color = clrGray;
    m_text_color = clrWhite;
    m_panel_x = 20;
    m_panel_y = 50;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CDayTradingUI::~CDayTradingUI()
{
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CDayTradingUI::Initialize(ENUM_BASE_CORNER corner, color bg_color, color active_color, 
                              color inactive_color, color text_color)
{
    m_corner = corner;
    m_bg_color = bg_color;
    m_active_color = active_color;
    m_inactive_color = inactive_color;
    m_text_color = text_color;
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建主面板                                                        |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateMainPanel()
{
    CreateBackground();
    CreateTitleSection();
    CreateRiskButtons();
    CreateTradeButtons();
    CreateInfoLabels();
    CreatePsychologySection();
    CreateVolatilitySection();
    CreateVegasSection();
    
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 销毁所有面板                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::DestroyAllPanels()
{
    // 删除主要UI对象
    ObjectDelete(0, "DTK_Background");
    ObjectDelete(0, "DTK_Title");
    
    // 删除风险按钮
    ObjectDelete(0, "DTK_TrialButton");
    ObjectDelete(0, "DTK_StandardButton");
    ObjectDelete(0, "DTK_HeavyButton");
    
    // 删除交易按钮
    ObjectDelete(0, "DTK_BuyButton");
    ObjectDelete(0, "DTK_SellButton");
    ObjectDelete(0, "DTK_HighBuyButton");
    ObjectDelete(0, "DTK_HighSellButton");
    ObjectDelete(0, "DTK_LowBuyButton");
    ObjectDelete(0, "DTK_LowSellButton");
    ObjectDelete(0, "DTK_CloseAllButton");
    
    // 删除信息标签
    ObjectDelete(0, "DTK_ModeLabel");
    ObjectDelete(0, "DTK_RiskLabel");
    ObjectDelete(0, "DTK_LossLabel");
    ObjectDelete(0, "DTK_ProfitLabel");
    ObjectDelete(0, "DTK_LotLabel");
    ObjectDelete(0, "DTK_SLLabel");
    ObjectDelete(0, "DTK_TPLabel");
    ObjectDelete(0, "DTK_ATRLabel");
    ObjectDelete(0, "DTK_BalanceLabel");
    ObjectDelete(0, "DTK_EquityLabel");
    ObjectDelete(0, "DTK_PositionsLabel");
    
    // 删除心理监控部分
    ObjectDelete(0, "DTK_PsychHeader");
    ObjectDelete(0, "DTK_PsychStatus");
    ObjectDelete(0, "DTK_PsychWarning");
    
    // 删除波动率分析部分
    ObjectDelete(0, "DTK_VolHeader");
    ObjectDelete(0, "DTK_VolSession");
    ObjectDelete(0, "DTK_VolRange");
    ObjectDelete(0, "DTK_VolCompletion");
    
    // 删除Vegas分析部分
    ObjectDelete(0, "DTK_VegasHeader");
    ObjectDelete(0, "DTK_VegasTrend");
    ObjectDelete(0, "DTK_VegasSignal");
    ObjectDelete(0, "DTK_VegasPhase");
    
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 更新主显示信息                                                    |
//+------------------------------------------------------------------+
void CDayTradingUI::UpdateMainDisplay(const STradeCalculation &calc, const SPsychologyState &psych, 
                                     const SVolatilityAnalysis &vol, const SVegasAnalysis &vegas)
{
    string mode_name = "";
    switch(calc.risk_mode)
    {
        case RISK_TRIAL: mode_name = "试仓"; break;
        case RISK_STANDARD: mode_name = "标准仓"; break;
        case RISK_HEAVY: mode_name = "重仓"; break;
    }
    
    // 更新基础信息
    ObjectSetString(0, "DTK_ModeLabel", OBJPROP_TEXT, "当前模式: " + mode_name);
    ObjectSetString(0, "DTK_RiskLabel", OBJPROP_TEXT, StringFormat("风险比例: %.1f%%", calc.risk_percent));
    ObjectSetString(0, "DTK_LossLabel", OBJPROP_TEXT, StringFormat("预估亏损: %.2f", calc.estimated_loss));
    ObjectSetString(0, "DTK_ProfitLabel", OBJPROP_TEXT, StringFormat("预估盈利: %.2f", calc.estimated_profit));
    ObjectSetString(0, "DTK_LotLabel", OBJPROP_TEXT, StringFormat("计算手数: %.2f", calc.lot_size));
    ObjectSetString(0, "DTK_SLLabel", OBJPROP_TEXT, StringFormat("止损价格: %.5f", calc.sl_price));
    ObjectSetString(0, "DTK_TPLabel", OBJPROP_TEXT, StringFormat("止盈价格: %.5f", calc.tp_price));
    ObjectSetString(0, "DTK_ATRLabel", OBJPROP_TEXT, StringFormat("ATR值: %.5f", calc.atr_value));
    
    // 更新账户信息
    ObjectSetString(0, "DTK_BalanceLabel", OBJPROP_TEXT, StringFormat("账户余额: %.2f", calc.account_balance));
    ObjectSetString(0, "DTK_EquityLabel", OBJPROP_TEXT, StringFormat("账户净值: %.2f", calc.account_equity));
    ObjectSetString(0, "DTK_PositionsLabel", OBJPROP_TEXT, StringFormat("当前持仓: %d", calc.total_positions));
    
    // 更新心理监控信息
    ObjectSetString(0, "DTK_PsychStatus", OBJPROP_TEXT, psych.status_text);
    ObjectSetInteger(0, "DTK_PsychStatus", OBJPROP_COLOR, psych.status_color);
    ObjectSetString(0, "DTK_PsychWarning", OBJPROP_TEXT, psych.warning_message);
    
    // 更新波动率分析信息
    ObjectSetString(0, "DTK_VolSession", OBJPROP_TEXT, "当前时段: " + vol.current_session);
    ObjectSetString(0, "DTK_VolRange", OBJPROP_TEXT, StringFormat("日内波幅: %.5f", vol.daily_range));
    ObjectSetString(0, "DTK_VolCompletion", OBJPROP_TEXT, StringFormat("完成度: %.1f%%", vol.completion_percent));
    
    // 更新Vegas分析信息
    ObjectSetString(0, "DTK_VegasTrend", OBJPROP_TEXT, "趋势方向: " + vegas.trend_direction);
    ObjectSetInteger(0, "DTK_VegasTrend", OBJPROP_COLOR, vegas.trend_color);
    ObjectSetString(0, "DTK_VegasSignal", OBJPROP_TEXT, "信号强度: " + vegas.signal_strength);
    ObjectSetString(0, "DTK_VegasPhase", OBJPROP_TEXT, "市场阶段: " + vegas.market_phase);
}

//+------------------------------------------------------------------+
//| 更新风险按钮状态                                                  |
//+------------------------------------------------------------------+
void CDayTradingUI::UpdateRiskButtons(ENUM_RISK_MODE active_mode)
{
    // 重置所有按钮颜色
    ObjectSetInteger(0, "DTK_TrialButton", OBJPROP_BGCOLOR, m_inactive_color);
    ObjectSetInteger(0, "DTK_StandardButton", OBJPROP_BGCOLOR, m_inactive_color);
    ObjectSetInteger(0, "DTK_HeavyButton", OBJPROP_BGCOLOR, m_inactive_color);
    
    // 设置激活按钮颜色
    switch(active_mode)
    {
        case RISK_TRIAL:
            ObjectSetInteger(0, "DTK_TrialButton", OBJPROP_BGCOLOR, m_active_color);
            break;
        case RISK_STANDARD:
            ObjectSetInteger(0, "DTK_StandardButton", OBJPROP_BGCOLOR, m_active_color);
            break;
        case RISK_HEAVY:
            ObjectSetInteger(0, "DTK_HeavyButton", OBJPROP_BGCOLOR, m_active_color);
            break;
    }
}

//+------------------------------------------------------------------+
//| 创建背景                                                          |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateBackground()
{
    ObjectCreate(0, "DTK_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    SetObjectPosition("DTK_Background", m_panel_x, m_panel_y, MAIN_PANEL_WIDTH, MAIN_PANEL_HEIGHT);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_BGCOLOR, m_bg_color);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_CORNER, m_corner);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "DTK_Background", OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建标题部分                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateTitleSection()
{
    CreateLabel("DTK_Title", "🚀 日内交易大杀器 V1.0", m_panel_x + MARGIN, m_panel_y + MARGIN, clrYellow, 12);
}

//+------------------------------------------------------------------+
//| 创建风险按钮                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateRiskButtons()
{
    int y_pos = m_panel_y + 40;
    
    CreateButton("DTK_TrialButton", "试仓", m_panel_x + MARGIN, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_inactive_color);
    CreateButton("DTK_StandardButton", "标准仓", m_panel_x + MARGIN + BUTTON_WIDTH + 5, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_active_color);
    CreateButton("DTK_HeavyButton", "重仓", m_panel_x + MARGIN + BUTTON_WIDTH * 2 + 10, y_pos, BUTTON_WIDTH, BUTTON_HEIGHT, m_inactive_color);
}

//+------------------------------------------------------------------+
//| 创建交易按钮                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateTradeButtons()
{
    int y_pos = m_panel_y + 75;
    
    // 市价交易按钮
    CreateButton("DTK_BuyButton", "市价BUY", m_panel_x + MARGIN, y_pos, 100, 25, clrGreen);
    CreateButton("DTK_SellButton", "市价SELL", m_panel_x + MARGIN + 110, y_pos, 100, 25, clrRed);
    CreateButton("DTK_CloseAllButton", "一键平仓", m_panel_x + MARGIN + 220, y_pos, 100, 25, clrOrange);
    
    // 高点上方挂单按钮
    y_pos += 30;
    CreateButton("DTK_HighBuyButton", "高点挂多", m_panel_x + MARGIN, y_pos, 100, 25, clrLimeGreen);
    CreateButton("DTK_HighSellButton", "高点挂空", m_panel_x + MARGIN + 110, y_pos, 100, 25, clrOrangeRed);
    
    // 低点下方挂单按钮
    y_pos += 30;
    CreateButton("DTK_LowBuyButton", "低点挂多", m_panel_x + MARGIN, y_pos, 100, 25, clrDodgerBlue);
    CreateButton("DTK_LowSellButton", "低点挂空", m_panel_x + MARGIN + 110, y_pos, 100, 25, clrCrimson);
}

//+------------------------------------------------------------------+
//| 创建信息标签                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateInfoLabels()
{
    int start_y = m_panel_y + 170;
    int line_spacing = 18;
    
    CreateSectionHeader("DTK_InfoHeader", "📊 交易信息", m_panel_x + MARGIN, start_y, clrCyan);
    
    CreateLabel("DTK_ModeLabel", "当前模式: 标准仓", m_panel_x + MARGIN, start_y + 20, m_text_color);
    CreateLabel("DTK_RiskLabel", "风险比例: 0.5%", m_panel_x + MARGIN, start_y + 38, m_text_color);
    CreateLabel("DTK_LossLabel", "预估亏损: 0.00", m_panel_x + MARGIN, start_y + 56, m_text_color);
    CreateLabel("DTK_ProfitLabel", "预估盈利: 0.00", m_panel_x + MARGIN, start_y + 74, m_text_color);
    CreateLabel("DTK_LotLabel", "计算手数: 0.00", m_panel_x + MARGIN, start_y + 92, m_text_color);
    CreateLabel("DTK_SLLabel", "止损价格: 0.00000", m_panel_x + MARGIN, start_y + 110, m_text_color);
    CreateLabel("DTK_TPLabel", "止盈价格: 0.00000", m_panel_x + MARGIN, start_y + 128, m_text_color);
    CreateLabel("DTK_ATRLabel", "ATR值: 0.00000", m_panel_x + MARGIN, start_y + 146, m_text_color);
    
    CreateLabel("DTK_BalanceLabel", "账户余额: 0.00", m_panel_x + MARGIN, start_y + 170, clrLightGreen);
    CreateLabel("DTK_EquityLabel", "账户净值: 0.00", m_panel_x + MARGIN, start_y + 188, clrLightGreen);
    CreateLabel("DTK_PositionsLabel", "当前持仓: 0", m_panel_x + MARGIN, start_y + 206, clrLightBlue);
}

//+------------------------------------------------------------------+
//| 创建心理监控部分                                                  |
//+------------------------------------------------------------------+
void CDayTradingUI::CreatePsychologySection()
{
    int start_y = m_panel_y + 400;
    
    CreateSectionHeader("DTK_PsychHeader", "🧠 交易心理", m_panel_x + MARGIN, start_y, clrMagenta);
    CreateLabel("DTK_PsychStatus", "心理状态: 正常", m_panel_x + MARGIN, start_y + 20, clrLightGreen);
    CreateLabel("DTK_PsychWarning", "无警告", m_panel_x + MARGIN, start_y + 38, clrGray);
}

//+------------------------------------------------------------------+
//| 创建波动率分析部分                                                |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateVolatilitySection()
{
    int start_y = m_panel_y + 470;
    
    CreateSectionHeader("DTK_VolHeader", "📈 波动率分析", m_panel_x + MARGIN, start_y, clrGold);
    CreateLabel("DTK_VolSession", "当前时段: 初始化中", m_panel_x + MARGIN, start_y + 20, m_text_color);
    CreateLabel("DTK_VolRange", "日内波幅: 计算中", m_panel_x + MARGIN, start_y + 38, m_text_color);
    CreateLabel("DTK_VolCompletion", "完成度: 0%", m_panel_x + MARGIN, start_y + 56, m_text_color);
}

//+------------------------------------------------------------------+
//| 创建Vegas分析部分                                                 |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateVegasSection()
{
    int start_y = m_panel_y + 540;
    
    CreateSectionHeader("DTK_VegasHeader", "🎯 Vegas隧道", m_panel_x + MARGIN, start_y, clrLightSeaGreen);
    CreateLabel("DTK_VegasTrend", "趋势方向: 分析中", m_panel_x + MARGIN, start_y + 20, m_text_color);
    CreateLabel("DTK_VegasSignal", "信号强度: 计算中", m_panel_x + MARGIN, start_y + 38, m_text_color);
    CreateLabel("DTK_VegasPhase", "市场阶段: 识别中", m_panel_x + MARGIN, start_y + 56, m_text_color);
}

//+------------------------------------------------------------------+
//| 设置对象位置                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::SetObjectPosition(string name, int x, int y, int width = 0, int height = 0)
{
    ObjectSetInteger(0, name, OBJPROP_CORNER, m_corner);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    
    if(width > 0)
        ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    if(height > 0)
        ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateButton(string name, string text, int x, int y, int width, int height, color bg_color)
{
    ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
    SetObjectPosition(name, x, y, width, height);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建标签                                                          |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateLabel(string name, string text, int x, int y, color text_color, int font_size = 9)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    SetObjectPosition(name, x, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建章节标题                                                      |
//+------------------------------------------------------------------+
void CDayTradingUI::CreateSectionHeader(string name, string text, int x, int y, color header_color)
{
    CreateLabel(name, text, x, y, header_color, 10);
}