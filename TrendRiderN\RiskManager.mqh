//+------------------------------------------------------------------+
//|                                              RiskManager.mqh |
//|                        Copyright 2024, TrendRider Development |
//|                                             https://mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TrendRider Development"
#property link      "https://mql5.com"
#property version   "2.00"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include "SignalManager.mqh"

//+------------------------------------------------------------------+
//| 风险管理配置结���体                                                 |
//+------------------------------------------------------------------+
struct RiskConfig
{
   double    normal_risk_percent;         // 正常风险百分比
   double    reduced_risk_percent;        // 降低风险百分比
   int       losing_streak_threshold;     // 连亏阈值
   double    max_daily_loss_percent;      // 最大日亏损百分比
   double    max_drawdown_percent;        // 最大回撤百分比
   int       max_positions;               // 最大持仓数量
   double    min_profit_for_pyramid;      // 加仓最小盈利
   bool      enable_pyramiding;           // 启用加仓
   
   // 构造函数
   RiskConfig()
   {
      normal_risk_percent = 2.0;
      reduced_risk_percent = 1.0;
      losing_streak_threshold = 3;
      max_daily_loss_percent = 5.0;
      max_drawdown_percent = 10.0;
      max_positions = 3;
      min_profit_for_pyramid = 50.0;
      enable_pyramiding = true;
   }
};

//+------------------------------------------------------------------+
//| 仓位计算结果结构体                                                 |
//+------------------------------------------------------------------+
struct PositionSizeResult
{
   double    lot_size;                    // 计算出的手数
   double    risk_amount;                 // 风险金额
   double    current_risk_percent;        // 当前风险百分比
   bool      is_valid;                    // 结果是否有效
   string    calculation_info;            // 计算信息
   
   // 构造函数
   PositionSizeResult()
   {
      lot_size = 0.0;
      risk_amount = 0.0;
      current_risk_percent = 0.0;
      is_valid = false;
      calculation_info = "";
   }
};

//+------------------------------------------------------------------+
//| 风险管理器类                                                      |
//+------------------------------------------------------------------+
class CRiskManager
{
private:
   // 成员变量
   string              m_symbol;                  // 交易品种
   ulong               m_magic_number;            // 魔术号
   RiskConfig          m_config;                  // 风险配置
   CTrade              m_trade;                   // 交易类
   CPositionInfo       m_position;                // 持仓信息类
   
   // 风险状态变量
   int                 m_consecutive_losses;      // 连续亏损次数
   double              m_daily_start_balance;     // 日开始余额
   datetime            m_last_trade_date;         // 最后交易日期
   bool                m_trading_enabled;         // 交易启用状态
   
   // 私有方法
   bool CheckDailyRiskLimits();
   void UpdateDailyStatus();
   double CalculateAccountRisk(double position_size, double stop_distance);
   bool ValidatePositionSize(double lot_size);
   
public:
   // 构造函数
   CRiskManager(string symbol, ulong magic_number);
   
   // 析构函数
   ~CRiskManager();
   
   // 设置风险配置
   void SetRiskConfig(const RiskConfig &config);
   
   // 获取风险配置
   RiskConfig GetRiskConfig() const { return m_config; }
   
   // 初始化风险管理器
   bool Initialize();
   
   // 计算仓位大小
   PositionSizeResult CalculatePositionSize(const TradingSignal &signal);
   
   // 检查是否允许交易
   bool IsTradingAllowed();
   
   // 检查是否允许加仓
   bool IsPyramidingAllowed();
   
   // 更新交易结果
   void UpdateTradeResult(double profit, bool is_winning_trade);
   
   // 获取当前持仓数量
   int GetCurrentPositionCount();
   
   // 获取总体风险评估
   double GetPortfolioRisk();
   
   // 获取风险管理状态
   string GetRiskManagerStatus();
   
   // 重置风险状态(新的一天)
   void ResetRiskStatus();
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CRiskManager::CRiskManager(string symbol, ulong magic_number)
{
   m_symbol = symbol;
   m_magic_number = magic_number;
   m_consecutive_losses = 0;
   m_daily_start_balance = 0.0;
   m_last_trade_date = 0;
   m_trading_enabled = true;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CRiskManager::~CRiskManager()
{
   // 清理资源
}

//+------------------------------------------------------------------+
//| 设置风险配置                                                      |
//+------------------------------------------------------------------+
void CRiskManager::SetRiskConfig(const RiskConfig &config)
{
   m_config = config;
}

//+------------------------------------------------------------------+
//| 初始化风险管理器                                                   |
//+------------------------------------------------------------------+
bool CRiskManager::Initialize()
{
   // 设置交易参数
   m_trade.SetExpertMagicNumber(m_magic_number);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(m_symbol);
   
   // 初始化风险状态
   m_daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   m_last_trade_date = TimeCurrent();
   m_consecutive_losses = 0;
   m_trading_enabled = true;
   
   Print("风险管理器初始化完成 | 品种:", m_symbol, " | 魔术号:", m_magic_number);
   Print("初始余额:", m_daily_start_balance, " | 正常风险:", m_config.normal_risk_percent, "%");
   
   return true;
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
PositionSizeResult CRiskManager::CalculatePositionSize(const TradingSignal &signal)
{
   PositionSizeResult result;
   
   if(!signal.is_valid)
   {
      result.calculation_info = "信号无效";
      return result;
   }
   
   // 确定当前风险百分比
   result.current_risk_percent = (m_consecutive_losses >= m_config.losing_streak_threshold) ? 
                                m_config.reduced_risk_percent : m_config.normal_risk_percent;
   
   // 计算风险金额
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   result.risk_amount = account_balance * (result.current_risk_percent / 100.0);
   
   // 计算止损距离
   double stop_distance = MathAbs(signal.entry_price - signal.stop_loss);
   if(stop_distance <= 0.0)
   {
      result.calculation_info = "止损距离无效";
      return result;
   }
   
   // 获取合约规格
   double tick_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_value <= 0.0 || tick_size <= 0.0)
   {
      result.calculation_info = "合约规格无效";
      return result;
   }
   
   // 计算基础手数
   result.lot_size = result.risk_amount / (stop_distance * tick_value / tick_size);
   
   // 标准化手数
   double min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   
   result.lot_size = MathMax(result.lot_size, min_lot);
   result.lot_size = MathMin(result.lot_size, max_lot);
   result.lot_size = NormalizeDouble(result.lot_size / lot_step, 0) * lot_step;
   
   // 验证最终手数
   result.is_valid = ValidatePositionSize(result.lot_size);
   
   // 生成计算信息
   result.calculation_info = StringFormat("风险:%.1f%% | 风险金额:%.2f | 止损距离:%.5f | 手数:%.2f",
                                        result.current_risk_percent, result.risk_amount, 
                                        stop_distance, result.lot_size);
   
   return result;
}

//+------------------------------------------------------------------+
//| 验证仓位大小                                                      |
//+------------------------------------------------------------------+
bool CRiskManager::ValidatePositionSize(double lot_size)
{
   if(lot_size <= 0.0)
      return false;
   
   double min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   if(lot_size < min_lot || lot_size > max_lot)
      return false;
   
   // 检查保证金要求
   double margin_required = 0.0;
   if(!OrderCalcMargin(ORDER_TYPE_BUY, m_symbol, lot_size, 
                      SymbolInfoDouble(m_symbol, SYMBOL_ASK), margin_required))
      return false;
   
   double free_margin = AccountInfoDouble(ACCOUNT_FREEMARGIN);
   if(margin_required > free_margin * 0.8) // 保留20%保证金缓冲
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查是否允许交易                                                   |
//+------------------------------------------------------------------+
bool CRiskManager::IsTradingAllowed()
{
   // 检查交易启用状态
   if(!m_trading_enabled)
      return false;
   
   // 检查日风险限制
   if(!CheckDailyRiskLimits())
   {
      m_trading_enabled = false;
      return false;
   }
   
   // 检查最大持仓限制
   if(GetCurrentPositionCount() >= m_config.max_positions)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查是否允许加仓                                                   |
//+------------------------------------------------------------------+
bool CRiskManager::IsPyramidingAllowed()
{
   if(!m_config.enable_pyramiding)
      return false;
   
   if(!IsTradingAllowed())
      return false;
   
   // 检查现有持仓是否盈利
   double total_profit = 0.0;
   int profitable_positions = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(m_position.SelectByIndex(i))
      {
         if(m_position.Symbol() == m_symbol && m_position.Magic() == m_magic_number)
         {
            double profit = m_position.Profit();
            total_profit += profit;
            
            if(profit > m_config.min_profit_for_pyramid)
               profitable_positions++;
         }
      }
   }
   
   return (profitable_positions > 0 && total_profit > 0);
}

//+------------------------------------------------------------------+
//| 更新交易结果                                                      |
//+------------------------------------------------------------------+
void CRiskManager::UpdateTradeResult(double profit, bool is_winning_trade)
{
   if(is_winning_trade)
   {
      m_consecutive_losses = 0;
      Print("交易盈利 | 盈利金额:", profit, " | 重置连续亏损计数");
   }
   else
   {
      m_consecutive_losses++;
      Print("交易亏损 | 亏损金额:", profit, " | 连续亏损次数:", m_consecutive_losses);
      
      if(m_consecutive_losses >= m_config.losing_streak_threshold)
      {
         Print("触发连续亏损阈值 | 风险降低至:", m_config.reduced_risk_percent, "%");
      }
   }
}

//+------------------------------------------------------------------+
//| 获取当前持仓数量                                                   |
//+------------------------------------------------------------------+
int CRiskManager::GetCurrentPositionCount()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(m_position.SelectByIndex(i))
      {
         if(m_position.Symbol() == m_symbol && m_position.Magic() == m_magic_number)
            count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| 检查日风险限制                                                     |
//+------------------------------------------------------------------+
bool CRiskManager::CheckDailyRiskLimits()
{
   UpdateDailyStatus();
   
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   // 检查日亏损限制
   if(m_daily_start_balance > 0)
   {
      double daily_loss_percent = ((m_daily_start_balance - current_balance) / m_daily_start_balance) * 100.0;
      
      if(daily_loss_percent >= m_config.max_daily_loss_percent)
      {
         Print("触发日亏损限制 | 当前亏损:", daily_loss_percent, "% | 限制:", m_config.max_daily_loss_percent, "%");
         return false;
      }
      
      // 检查回撤限制
      double drawdown_percent = ((m_daily_start_balance - current_equity) / m_daily_start_balance) * 100.0;
      
      if(drawdown_percent >= m_config.max_drawdown_percent)
      {
         Print("触发回撤限制 | 当前回撤:", drawdown_percent, "% | 限制:", m_config.max_drawdown_percent, "%");
         return false;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 更新日状态                                                        |
//+------------------------------------------------------------------+
void CRiskManager::UpdateDailyStatus()
{
   datetime current_date = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_date, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   datetime day_start = StructToTime(dt);
   
   if(day_start != m_last_trade_date)
   {
      ResetRiskStatus();
   }
}

//+------------------------------------------------------------------+
//| 重置风险状态                                                      |
//+------------------------------------------------------------------+
void CRiskManager::ResetRiskStatus()
{
   datetime current_date = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_date, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   
   m_daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   m_last_trade_date = StructToTime(dt);
   m_trading_enabled = true;
   
   Print("新交易日开始 | 重置风险状态 | 日开始余额:", m_daily_start_balance);
}

//+------------------------------------------------------------------+
//| 获取组合风险评估                                                   |
//+------------------------------------------------------------------+
double CRiskManager::GetPortfolioRisk()
{
   double total_risk = 0.0;
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   if(account_balance <= 0)
      return 0.0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(m_position.SelectByIndex(i))
      {
         if(m_position.Symbol() == m_symbol && m_position.Magic() == m_magic_number)
         {
            double position_risk = MathAbs(m_position.PriceOpen() - m_position.StopLoss()) * 
                                  m_position.Volume() * 
                                  SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE) / 
                                  SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
            
            total_risk += position_risk;
         }
      }
   }
   
   return (total_risk / account_balance) * 100.0; // 返回百分比
}

//+------------------------------------------------------------------+
//| 获取风险管理状态                                                   |
//+------------------------------------------------------------------+
string CRiskManager::GetRiskManagerStatus()
{
   string status = "=== 风险管理状态 ===\n";
   status += StringFormat("交易启用: %s\n", m_trading_enabled ? "是" : "否");
   status += StringFormat("连续亏损: %d次\n", m_consecutive_losses);
   
   double current_risk = (m_consecutive_losses >= m_config.losing_streak_threshold) ? 
                        m_config.reduced_risk_percent : m_config.normal_risk_percent;
   status += StringFormat("当前风险: %.1f%%\n", current_risk);
   
   status += StringFormat("当前持仓: %d/%d\n", GetCurrentPositionCount(), m_config.max_positions);
   status += StringFormat("组合风险: %.2f%%\n", GetPortfolioRisk());
   
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(m_daily_start_balance > 0)
   {
      double daily_pnl = current_balance - m_daily_start_balance;
      double daily_pnl_percent = (daily_pnl / m_daily_start_balance) * 100.0;
      status += StringFormat("日盈亏: %.2f (%.2f%%)", daily_pnl, daily_pnl_percent);
   }
   
   return status;
}