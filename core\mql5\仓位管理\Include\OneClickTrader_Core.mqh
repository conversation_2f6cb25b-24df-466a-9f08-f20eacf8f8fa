//+------------------------------------------------------------------+
//|                                       OneClickTrader_Core.mqh   |
//|                                    Copyright 2025, Your Company |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| 核心交易类                                                        |
//+------------------------------------------------------------------+
class COneClickTraderCore
{
private:
    CTrade m_trade;
    int m_magic_number;
    int m_slippage;
    
    // 缓存的Symbol信息（减少重复调用）
    double m_tick_size;
    double m_min_lot;
    double m_max_lot;
    double m_lot_step;
    
public:
    COneClickTraderCore();
    ~COneClickTraderCore();
    
    bool Initialize(int magic_number, int slippage);
    bool ExecuteBuy(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteSell(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteHighBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteHighSellStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteLowBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    bool ExecuteLowSellStop(double lot_size, double sl_price, double tp_price, double risk_percent);
    
private:
    void CacheSymbolInfo();
    bool ValidateTradeParameters(double lot_size, double sl_price, double tp_price);
    void LogTradeError(int error_code, string operation);
    double GetCurrentBarHigh();
    double GetCurrentBarLow();
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
COneClickTraderCore::COneClickTraderCore()
{
    m_magic_number = 0;
    m_slippage = 3;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
COneClickTraderCore::~COneClickTraderCore()
{
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool COneClickTraderCore::Initialize(int magic_number, int slippage)
{
    m_magic_number = magic_number;
    m_slippage = slippage;
    
    m_trade.SetExpertMagicNumber(m_magic_number);
    m_trade.SetDeviationInPoints(m_slippage);
    m_trade.SetTypeFilling(ORDER_FILLING_FOK);
    m_trade.SetTypeFillingBySymbol(Symbol());
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行买入交易                                                      |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteBuy(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    string comment = StringFormat("多头|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.Buy(lot_size, Symbol(), ask, sl_price, tp_price, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行卖出交易                                                      |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteSell(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 对于卖出订单，重新计算止损止盈
    double sl_distance = MathAbs(sl_price - bid);
    double tp_distance = MathAbs(tp_price - bid);
    
    double sell_sl = bid + sl_distance;  // 卖出止损在当前价上方
    double sell_tp = bid - tp_distance;  // 卖出止盈在当前价下方
    
    string comment = StringFormat("空头|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.Sell(lot_size, Symbol(), bid, sell_sl, sell_tp, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行高点上方买入挂单（突破做多）                                  |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteHighBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_high = GetCurrentBarHigh();
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double entry_price = current_high + tick_size;
    
    // 对于买入挂单，重新计算止损止盈
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double buy_sl = entry_price - sl_distance;  // 买入止损在入场价下方
    double buy_tp = entry_price + tp_distance;  // 买入止盈在入场价上方
    
    string comment = StringFormat("高点挂多|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.BuyStop(lot_size, entry_price, Symbol(), buy_sl, buy_tp, 
                                 ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "高点挂单买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行高点上方卖出挂单（假突破做空）                                |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteHighSellStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_high = GetCurrentBarHigh();
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double entry_price = current_high + tick_size;
    
    // 对于卖出订单，重新计算止损止盈
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double sell_sl = entry_price + sl_distance;  // 卖出止损在入场价上方
    double sell_tp = entry_price - tp_distance;  // 卖出止盈在入场价下方
    
    string comment = StringFormat("高点挂空|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.SellLimit(lot_size, entry_price, Symbol(), sell_sl, sell_tp, 
                                   ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "高点挂单卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行低点下方买入挂单（假跌破做多）                                |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteLowBuyStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_low = GetCurrentBarLow();
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double entry_price = current_low - tick_size;
    
    // 对于买入挂单，重新计算止损止盈
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double buy_sl = entry_price - sl_distance;  // 买入止损在入场价下方
    double buy_tp = entry_price + tp_distance;  // 买入止盈在入场价上方
    
    string comment = StringFormat("低点挂多|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.BuyLimit(lot_size, entry_price, Symbol(), buy_sl, buy_tp, 
                                  ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "低点挂单买入");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 执行低点下方卖出挂单（跌破做空）                                  |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ExecuteLowSellStop(double lot_size, double sl_price, double tp_price, double risk_percent)
{
    if(!ValidateTradeParameters(lot_size, sl_price, tp_price))
        return false;
    
    double current_low = GetCurrentBarLow();
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double entry_price = current_low - tick_size;
    
    // 对于卖出订单，重新计算止损止盈
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl_distance = MathAbs(sl_price - current_price);
    double tp_distance = MathAbs(tp_price - current_price);
    
    double sell_sl = entry_price + sl_distance;  // 卖出止损在入场价上方
    double sell_tp = entry_price - tp_distance;  // 卖出止盈在入场价下方
    
    string comment = StringFormat("低点挂空|%.1f%%|%.2f手", risk_percent, lot_size);
    bool result = m_trade.SellStop(lot_size, entry_price, Symbol(), sell_sl, sell_tp, 
                                  ORDER_TIME_GTC, 0, comment);
    
    if(!result)
    {
        LogTradeError(m_trade.ResultRetcode(), "低点挂单卖出");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取前一根K线最高价                                               |
//+------------------------------------------------------------------+
double COneClickTraderCore::GetCurrentBarHigh()
{
    double high_buffer[1];
    if(CopyHigh(Symbol(), PERIOD_CURRENT, 1, 1, high_buffer) <= 0)
        return 0;
    
    return high_buffer[0];
}

//+------------------------------------------------------------------+
//| 获取前一根K线最低价                                               |
//+------------------------------------------------------------------+
double COneClickTraderCore::GetCurrentBarLow()
{
    double low_buffer[1];
    if(CopyLow(Symbol(), PERIOD_CURRENT, 1, 1, low_buffer) <= 0)
        return 0;
    
    return low_buffer[0];
}

//+------------------------------------------------------------------+
//| 验证交易参数                                                      |
//+------------------------------------------------------------------+
bool COneClickTraderCore::ValidateTradeParameters(double lot_size, double sl_price, double tp_price)
{
    // 检查手数
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    if(lot_size < min_lot || lot_size > max_lot)
    {
        Print(StringFormat("手数超出范围: %.2f (允许范围: %.2f - %.2f)", lot_size, min_lot, max_lot));
        return false;
    }
    
    // 检查账户余额
    double margin_required = 0;
    if(!OrderCalcMargin(ORDER_TYPE_BUY, Symbol(), lot_size, SymbolInfoDouble(Symbol(), SYMBOL_ASK), margin_required))
    {
        Print("无法计算保证金需求");
        return false;
    }
    
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(margin_required > free_margin)
    {
        Print(StringFormat("保证金不足: 需要%.2f, 可用%.2f", margin_required, free_margin));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 记录交易错误                                                      |
//+------------------------------------------------------------------+
void COneClickTraderCore::LogTradeError(int error_code, string operation)
{
    string error_desc = "";
    
    switch(error_code)
    {
        case TRADE_RETCODE_REQUOTE:
            error_desc = "重新报价";
            break;
        case TRADE_RETCODE_REJECT:
            error_desc = "请求被拒绝";
            break;
        case TRADE_RETCODE_MARKET_CLOSED:
            error_desc = "市场关闭";
            break;
        case TRADE_RETCODE_INVALID_VOLUME:
            error_desc = "无效手数";
            break;
        case TRADE_RETCODE_NO_MONEY:
            error_desc = "资金不足";
            break;
        case TRADE_RETCODE_INVALID_STOPS:
            error_desc = "无效止损/止盈";
            break;
        default:
            error_desc = "未知错误";
            break;
    }
    
    Print(StringFormat("%s交易失败: %s (错误代码: %d)", operation, error_desc, error_code));
}