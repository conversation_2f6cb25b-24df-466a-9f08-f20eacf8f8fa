//+------------------------------------------------------------------+
//|                                  OneClickTrader_Calculator.mqh  |
//|                                    Copyright 2025, Your Company |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"

//--- 风险模式枚举
enum ENUM_RISK_MODE
{
    RISK_TRIAL = 0,     // 试仓
    RISK_STANDARD = 1,  // 标准仓
    RISK_HEAVY = 2      // 重仓
};

//--- 交易计算结果结构
struct STradeCalculation
{
    ENUM_RISK_MODE risk_mode;      // 当前风险模式
    double risk_percent;           // 风险百分比
    double lot_size;               // 计算手数
    double sl_price;               // 止损价格
    double tp_price;               // 止盈价格
    double estimated_loss;         // 预估亏损
    double estimated_profit;       // 预估盈利
    double atr_value;              // ATR值
    double sl_distance;            // 止损距离
    double tp_distance;            // 止盈距离
};

//+------------------------------------------------------------------+
//| 仓位计算器类                                                      |
//+------------------------------------------------------------------+
class CPositionCalculator
{
private:
    int m_atr_handle;              // ATR指标句柄
    int m_atr_period;              // ATR周期
    double m_atr_multiplier;       // ATR乘数
    double m_risk_reward_ratio;    // 风险回报比
    
    double m_trial_risk;           // 试仓风险百分比
    double m_standard_risk;        // 标准仓风险百分比
    double m_heavy_risk;           // 重仓风险百分比
    
    ENUM_RISK_MODE m_current_mode; // 当前风险模式
    
public:
    CPositionCalculator();
    ~CPositionCalculator();
    
    bool Initialize(int atr_period, double atr_multiplier, double rrr,
                   double trial_risk, double standard_risk, double heavy_risk);
    bool Calculate(STradeCalculation &calc);
    void SetRiskMode(ENUM_RISK_MODE mode);
    ENUM_RISK_MODE GetRiskMode() const { return m_current_mode; }
    
private:
    double GetATRValue();
    double GetCurrentRiskPercent();
    string GetRiskModeName(ENUM_RISK_MODE mode);
    double NormalizeLotSize(double lot_size);
    double CalculatePointValue();
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CPositionCalculator::CPositionCalculator()
{
    m_atr_handle = INVALID_HANDLE;
    m_atr_period = 14;
    m_atr_multiplier = 2.0;
    m_risk_reward_ratio = 2.0;
    m_trial_risk = 0.2;
    m_standard_risk = 0.5;
    m_heavy_risk = 2.0;
    m_current_mode = RISK_STANDARD;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CPositionCalculator::~CPositionCalculator()
{
    if(m_atr_handle != INVALID_HANDLE)
        IndicatorRelease(m_atr_handle);
}

//+------------------------------------------------------------------+
//| 初始化                                                            |
//+------------------------------------------------------------------+
bool CPositionCalculator::Initialize(int atr_period, double atr_multiplier, double rrr,
                                   double trial_risk, double standard_risk, double heavy_risk)
{
    m_atr_period = atr_period;
    m_atr_multiplier = atr_multiplier;
    m_risk_reward_ratio = rrr;
    m_trial_risk = trial_risk;
    m_standard_risk = standard_risk;
    m_heavy_risk = heavy_risk;
    
    // 创建ATR指标
    m_atr_handle = iATR(Symbol(), PERIOD_CURRENT, m_atr_period);
    if(m_atr_handle == INVALID_HANDLE)
    {
        Print("创建ATR指标失败");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算交易参数                                                      |
//+------------------------------------------------------------------+
bool CPositionCalculator::Calculate(STradeCalculation &calc)
{
    // 获取ATR值
    double atr_value = GetATRValue();
    if(atr_value <= 0)
        return false;
    
    // 获取当前价格
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    if(current_price <= 0)
        return false;
    
    // 计算止损距离
    double sl_distance = atr_value * m_atr_multiplier;
    double tp_distance = sl_distance * m_risk_reward_ratio;
    
    // 获取当前风险百分比
    double risk_percent = GetCurrentRiskPercent();
    
    // 计算风险金额
    double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double risk_amount = account_equity * risk_percent / 100.0;
    
    // 计算点值
    double point_value = CalculatePointValue();
    if(point_value <= 0)
        return false;
    
    // 计算手数 - 防止除0错误
    double lot_size;
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    if(point <= 0 || sl_distance <= 0 || point_value <= 0)
    {
        Print("警告: 计算参数异常，使用最小手数");
        lot_size = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    }
    else
    {
        double denominator = (sl_distance / point) * point_value;
        if(denominator <= 0)
        {
            Print("警告: 分母为0，使用最小手数");
            lot_size = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
        }
        else
        {
            lot_size = risk_amount / denominator;
        }
    }
    
    lot_size = NormalizeLotSize(lot_size);
    
    // 填充结果结构
    calc.risk_mode = m_current_mode;
    calc.risk_percent = risk_percent;
    calc.lot_size = lot_size;
    calc.atr_value = atr_value;
    calc.sl_distance = sl_distance;
    calc.tp_distance = tp_distance;
    
    // 计算买入的止损止盈价格（默认显示买入价格）
    calc.sl_price = current_price - sl_distance;
    calc.tp_price = current_price + tp_distance;
    
    // 计算预估盈亏
    calc.estimated_loss = sl_distance / SymbolInfoDouble(Symbol(), SYMBOL_POINT) * point_value * lot_size;
    calc.estimated_profit = tp_distance / SymbolInfoDouble(Symbol(), SYMBOL_POINT) * point_value * lot_size;
    
    return true;
}

//+------------------------------------------------------------------+
//| 设置风险模式                                                      |
//+------------------------------------------------------------------+
void CPositionCalculator::SetRiskMode(ENUM_RISK_MODE mode)
{
    m_current_mode = mode;
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                         |
//+------------------------------------------------------------------+
double CPositionCalculator::GetATRValue()
{
    if(m_atr_handle == INVALID_HANDLE)
        return 0;
    
    double atr_buffer[1];
    if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
        return 0;
    
    return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| 获取当前风险百分比                                                |
//+------------------------------------------------------------------+
double CPositionCalculator::GetCurrentRiskPercent()
{
    switch(m_current_mode)
    {
        case RISK_TRIAL:
            return m_trial_risk;
        case RISK_STANDARD:
            return m_standard_risk;
        case RISK_HEAVY:
            return m_heavy_risk;
        default:
            return m_standard_risk;
    }
}

//+------------------------------------------------------------------+
//| 获取风险模式名称                                                  |
//+------------------------------------------------------------------+
string CPositionCalculator::GetRiskModeName(ENUM_RISK_MODE mode)
{
    switch(mode)
    {
        case RISK_TRIAL:
            return "试仓";
        case RISK_STANDARD:
            return "标准仓";
        case RISK_HEAVY:
            return "重仓";
        default:
            return "标准仓";
    }
}

//+------------------------------------------------------------------+
//| 标准化手数                                                        |
//+------------------------------------------------------------------+
double CPositionCalculator::NormalizeLotSize(double lot_size)
{
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    // 确保手数不小于最小值
    if(lot_size < min_lot)
        lot_size = min_lot;
    
    // 确保手数不大于最大值
    if(lot_size > max_lot)
        lot_size = max_lot;
    
    // 按步长调整
    lot_size = MathRound(lot_size / lot_step) * lot_step;
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| 计算点值                                                          |
//+------------------------------------------------------------------+
double CPositionCalculator::CalculatePointValue()
{
    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    
    // 防止除0错误
    if(tick_size <= 0 || point <= 0)
    {
        Print("警告: tick_size或point为0，使用默认点值");
        return 1.0; // 返回默认点值
    }
    
    return tick_value * point / tick_size;
}
