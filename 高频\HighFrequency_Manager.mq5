//+------------------------------------------------------------------+
//|                                   HighFrequency_Manager.mq5 |
//|                    🎛️ 高频EA管理器 - 统一监控和风控           |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

//--- 输入参数
input group "=== 🎛️ 管理器设置 ==="
input bool     EnableManager = true;        // 启用管理器
input double   TotalMaxLoss = 500.0;        // 总最大亏损($)
input double   TotalMaxDrawdown = 10.0;     // 总最大回撤(%)
input int      ReportInterval = 60;         // 报告间隔(秒)

input group "=== 📊 EA监控设置 ==="
input bool     MonitorSpreadEA = true;      // 监控价差EA
input bool     MonitorMomentumEA = true;    // 监控动量EA
input bool     MonitorVolumeEA = true;      // 监控成交量EA
input int      SpreadMagic = 888888;        // 价差EA魔术数字
input int      MomentumMagic = 999999;      // 动量EA魔术数字
input int      VolumeMagic = 777777;        // 成交量EA魔术数字

//--- 全局变量
struct EAStats {
    string name;
    int magic;
    int positions;
    double profit;
    double drawdown;
    int trades_today;
    datetime last_trade;
    bool is_active;
};

EAStats ea_stats[3];
double initial_balance = 0;
double total_profit = 0;
double max_balance = 0;
double current_drawdown = 0;
datetime last_report = 0;

//+------------------------------------------------------------------+
//| 指标初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    EventSetTimer(5);  // 每5秒检查一次
    
    initial_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    max_balance = initial_balance;
    
    // 初始化EA统计
    InitializeEAStats();
    
    Print("🎛️🎛️🎛️ 高频EA管理器已启动！");
    Print("💰 初始余额: $", DoubleToString(initial_balance, 2));
    Print("🛡️ 总风控已激活");
    Print("📊 监控EA数量: ", GetActiveEACount());
    Print("========================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 初始化EA统计
//+------------------------------------------------------------------+
void InitializeEAStats() {
    // 价差回归EA
    ea_stats[0].name = "价差回归EA";
    ea_stats[0].magic = SpreadMagic;
    ea_stats[0].is_active = MonitorSpreadEA;
    
    // 动量反转EA
    ea_stats[1].name = "动量反转EA";
    ea_stats[1].magic = MomentumMagic;
    ea_stats[1].is_active = MonitorMomentumEA;
    
    // 成交量异常EA
    ea_stats[2].name = "成交量异常EA";
    ea_stats[2].magic = VolumeMagic;
    ea_stats[2].is_active = MonitorVolumeEA;
    
    // 重置统计
    for(int i = 0; i < 3; i++) {
        ea_stats[i].positions = 0;
        ea_stats[i].profit = 0;
        ea_stats[i].drawdown = 0;
        ea_stats[i].trades_today = 0;
        ea_stats[i].last_trade = 0;
    }
}

//+------------------------------------------------------------------+
//| 定时器函数
//+------------------------------------------------------------------+
void OnTimer() {
    if(!EnableManager) return;
    
    // 更新EA统计
    UpdateEAStatistics();
    
    // 检查总体风控
    CheckGlobalRiskControl();
    
    // 定期报告
    if(TimeCurrent() - last_report >= ReportInterval) {
        GenerateReport();
        last_report = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| 更新EA统计
//+------------------------------------------------------------------+
void UpdateEAStatistics() {
    for(int i = 0; i < 3; i++) {
        if(!ea_stats[i].is_active) continue;
        
        // 重置统计
        ea_stats[i].positions = 0;
        ea_stats[i].profit = 0;
        ea_stats[i].trades_today = 0;
        
        // 统计持仓
        for(int j = 0; j < PositionsTotal(); j++) {
            if(PositionGetSymbol(j) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == ea_stats[i].magic) {
                ea_stats[i].positions++;
                ea_stats[i].profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
        
        // 统计今日交易（简化版本）
        ea_stats[i].trades_today = CountTodayTrades(ea_stats[i].magic);
    }
    
    // 计算总盈亏和回撤
    CalculateGlobalStats();
}

//+------------------------------------------------------------------+
//| 统计今日交易数量
//+------------------------------------------------------------------+
int CountTodayTrades(int magic) {
    int count = 0;
    datetime today_start = iTime(_Symbol, PERIOD_D1, 0);
    
    // 这里应该遍历历史交易，简化处理
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionGetSymbol(i) == _Symbol && 
           PositionGetInteger(POSITION_MAGIC) == magic &&
           PositionGetInteger(POSITION_TIME) >= today_start) {
            count++;
        }
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| 计算全局统计
//+------------------------------------------------------------------+
void CalculateGlobalStats() {
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    total_profit = current_balance - initial_balance;
    
    // 更新最大余额
    if(current_balance > max_balance) {
        max_balance = current_balance;
    }
    
    // 计算当前回撤
    current_drawdown = (max_balance - current_equity) / max_balance * 100;
}

//+------------------------------------------------------------------+
//| 检查全局风控
//+------------------------------------------------------------------+
void CheckGlobalRiskControl() {
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // 检查总亏损限制
    if(total_profit < -TotalMaxLoss) {
        Print("🚨🚨🚨 达到总最大亏损限制！");
        Print("💰 当前亏损: $", DoubleToString(-total_profit, 2));
        Print("🛑 建议立即停止所有EA！");
        
        // 可以在这里添加自动关闭所有持仓的代码
        EmergencyCloseAllPositions();
    }
    
    // 检查总回撤限制
    if(current_drawdown > TotalMaxDrawdown) {
        Print("🚨🚨🚨 达到总最大回撤限制！");
        Print("📉 当前回撤: ", DoubleToString(current_drawdown, 2), "%");
        Print("🛑 建议暂停交易！");
    }
}

//+------------------------------------------------------------------+
//| 紧急关闭所有持仓
//+------------------------------------------------------------------+
void EmergencyCloseAllPositions() {
    Print("🚨 执行紧急平仓程序...");
    
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double volume = PositionGetDouble(POSITION_VOLUME);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            MqlTradeRequest request = {};
            MqlTradeResult result = {};
            
            request.action = TRADE_ACTION_DEAL;
            request.position = ticket;
            request.symbol = _Symbol;
            request.volume = volume;
            request.type = (type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
            request.price = (type == POSITION_TYPE_BUY) ? 
                           SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                           SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            request.comment = "紧急平仓";
            
            if(OrderSend(request, result)) {
                Print("✅ 紧急平仓成功: Ticket ", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 生成报告
//+------------------------------------------------------------------+
void GenerateReport() {
    Print("========================================");
    Print("📊 高频EA管理器报告 - ", TimeToString(TimeCurrent()));
    Print("========================================");
    
    // 总体统计
    PrintFormat("💰 账户余额: $%.2f (初始: $%.2f)", 
               AccountInfoDouble(ACCOUNT_BALANCE), initial_balance);
    PrintFormat("📈 总盈亏: $%.2f", total_profit);
    PrintFormat("📉 当前回撤: %.2f%%", current_drawdown);
    PrintFormat("⚡ 当前净值: $%.2f", AccountInfoDouble(ACCOUNT_EQUITY));
    
    Print("----------------------------------------");
    Print("🤖 各EA详细统计:");
    
    // 各EA统计
    for(int i = 0; i < 3; i++) {
        if(!ea_stats[i].is_active) continue;
        
        PrintFormat("🔹 %s (魔术号:%d)", ea_stats[i].name, ea_stats[i].magic);
        PrintFormat("   持仓数: %d | 浮盈: $%.2f | 今日交易: %d", 
                   ea_stats[i].positions, ea_stats[i].profit, ea_stats[i].trades_today);
    }
    
    Print("----------------------------------------");
    
    // 市场状态
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - 
                    SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    PrintFormat("📊 市场状态 - 品种: %s | 价差: %.1f点", _Symbol, spread);
    
    Print("========================================");
}

//+------------------------------------------------------------------+
//| 获取活跃EA数量
//+------------------------------------------------------------------+
int GetActiveEACount() {
    int count = 0;
    for(int i = 0; i < 3; i++) {
        if(ea_stats[i].is_active) count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| OnCalculate函数 - 用于实时监控
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // 实时风控检查
    static datetime last_check = 0;
    if(TimeCurrent() - last_check >= 10) {  // 每10秒检查一次
        CheckGlobalRiskControl();
        last_check = TimeCurrent();
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    EventKillTimer();
    
    // 最终报告
    GenerateReport();
    
    Print("========================================");
    Print("🎛️ 高频EA管理器已停止");
    PrintFormat("⏱️ 运行时长: %d分钟", (TimeCurrent() - last_report) / 60);
    Print("📊 感谢使用高频交易管理系统！");
    Print("========================================");
}