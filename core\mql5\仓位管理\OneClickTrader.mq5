//+------------------------------------------------------------------+
//|                                              OneClickTrader.mq5 |
//|                                    Copyright 2025, Your Company |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "一键交易与风险管理EA - 专业仓位计算和交易执行"

#include "Include/OneClickTrader_Core.mqh"
#include "Include/OneClickTrader_UI.mqh"
#include "Include/OneClickTrader_Calculator.mqh"

//--- 输入参数
input group "=== 风险管理设置 ==="
input double Trial_Risk_Percent = 0.2;        // 试仓风险百分比 (%)
input double Standard_Risk_Percent = 0.5;     // 标准仓风险百分比 (%)
input double Heavy_Risk_Percent = 2.0;        // 重仓风险百分比 (%)

input group "=== 技术指标设置 ==="
input int ATR_Period = 14;                    // ATR周期
input double ATR_Multiplier = 2.0;            // ATR乘数
input double Risk_Reward_Ratio = 2.0;         // 风险回报比 (1:X)

input group "=== 交易设置 ==="
input int Magic_Number = 888888;              // 魔术手号码
input int Slippage = 3;                       // 允许滑点 (点)

input group "=== 界面设置 ==="
input ENUM_BASE_CORNER Panel_Corner = CORNER_LEFT_UPPER;  // UI面板位置
input color Panel_Background = clrDarkSlateGray;           // 面板背景色
input color Button_Active = clrLimeGreen;                  // 激活按钮颜色
input color Button_Inactive = clrGray;                     // 非激活按钮颜色
input color Text_Color = clrWhite;                         // 文字颜色

//--- 全局变量
COneClickTraderCore* g_Core = NULL;
COneClickTraderUI* g_UI = NULL;
CPositionCalculator* g_Calculator = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化核心组件
    g_Core = new COneClickTraderCore();
    g_UI = new COneClickTraderUI();
    g_Calculator = new CPositionCalculator();
    
    // 设置参数
    if(!g_Core.Initialize(Magic_Number, Slippage))
    {
        Print("核心组件初始化失败");
        return INIT_FAILED;
    }
    
    if(!g_Calculator.Initialize(ATR_Period, ATR_Multiplier, Risk_Reward_Ratio,
                               Trial_Risk_Percent, Standard_Risk_Percent, Heavy_Risk_Percent))
    {
        Print("计算器初始化失败");
        return INIT_FAILED;
    }
    
    if(!g_UI.Initialize(Panel_Corner, Panel_Background, Button_Active, 
                       Button_Inactive, Text_Color))
    {
        Print("UI初始化失败");
        return INIT_FAILED;
    }
    
    // 创建UI面板
    g_UI.CreatePanel();
    
    // 初始计算并更新显示
    UpdateDisplay();
    
    Print("一键交易EA初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理UI对象
    if(g_UI != NULL)
    {
        g_UI.DestroyPanel();
        delete g_UI;
        g_UI = NULL;
    }
    
    // 清理其他对象
    if(g_Core != NULL)
    {
        delete g_Core;
        g_Core = NULL;
    }
    
    if(g_Calculator != NULL)
    {
        delete g_Calculator;
        g_Calculator = NULL;
    }
    
    Print("一键交易EA已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 优化更新频率：每5秒或价格变化超过一定幅度时更新
    static datetime last_update = 0;
    static double last_price = 0;
    
    datetime current_time = TimeCurrent();
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 每5秒更新一次，或价格变化超过0.1%时立即更新
    bool time_update = (current_time - last_update >= 5);
    bool price_update = false;
    
    // 防止除0错误
    if(last_price > 0 && current_price > 0)
    {
        price_update = (MathAbs(current_price - last_price) / last_price > 0.001);
    }
    
    if(time_update || price_update || last_price == 0)
    {
        UpdateDisplay();
        last_update = current_time;
        last_price = current_price;
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(g_UI == NULL) return;
    
    // 处理UI事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        string clicked_object = sparam;
        
        // 风险档位按钮
        if(clicked_object == "OCT_TrialButton")
        {
            g_Calculator.SetRiskMode(RISK_TRIAL);
            g_UI.UpdateRiskButtons(RISK_TRIAL);
            UpdateDisplay();
        }
        else if(clicked_object == "OCT_StandardButton")
        {
            g_Calculator.SetRiskMode(RISK_STANDARD);
            g_UI.UpdateRiskButtons(RISK_STANDARD);
            UpdateDisplay();
        }
        else if(clicked_object == "OCT_HeavyButton")
        {
            g_Calculator.SetRiskMode(RISK_HEAVY);
            g_UI.UpdateRiskButtons(RISK_HEAVY);
            UpdateDisplay();
        }
        // 市价交易按钮
        else if(clicked_object == "OCT_BuyButton")
        {
            ExecuteTrade(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "OCT_SellButton")
        {
            ExecuteTrade(ORDER_TYPE_SELL);
        }
        // 高点上方挂单按钮
        else if(clicked_object == "OCT_HighBuyButton")
        {
            ExecuteHighPendingOrder(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "OCT_HighSellButton")
        {
            ExecuteHighPendingOrder(ORDER_TYPE_SELL);
        }
        // 低点下方挂单按钮
        else if(clicked_object == "OCT_LowBuyButton")
        {
            ExecuteLowPendingOrder(ORDER_TYPE_BUY);
        }
        else if(clicked_object == "OCT_LowSellButton")
        {
            ExecuteLowPendingOrder(ORDER_TYPE_SELL);
        }
        
        ChartRedraw();
    }
}

//+------------------------------------------------------------------+
//| 更新显示信息                                                      |
//+------------------------------------------------------------------+
void UpdateDisplay()
{
    if(g_Calculator == NULL || g_UI == NULL) return;
    
    // 获取计算结果
    STradeCalculation calc;
    if(g_Calculator.Calculate(calc))
    {
        g_UI.UpdateDisplay(calc);
    }
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL || g_Calculator == NULL) return;
    
    // 获取最新计算结果
    STradeCalculation calc;
    if(!g_Calculator.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    // 执行交易
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteBuy(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteSell(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出";
        Print(StringFormat("交易执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                          direction, calc.lot_size, calc.sl_price, calc.tp_price));
    }
}

//+------------------------------------------------------------------+
//| 执行高点上方挂单交易                                              |
//+------------------------------------------------------------------+
void ExecuteHighPendingOrder(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL || g_Calculator == NULL) return;
    
    // 获取最新计算结果
    STradeCalculation calc;
    if(!g_Calculator.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    // 执行高点挂单
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteHighBuyStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteHighSellStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "高点挂多" : "高点挂空";
        Print(StringFormat("挂单执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                          direction, calc.lot_size, calc.sl_price, calc.tp_price));
    }
}

//+------------------------------------------------------------------+
//| 执行低点下方挂单交易                                              |
//+------------------------------------------------------------------+
void ExecuteLowPendingOrder(ENUM_ORDER_TYPE order_type)
{
    if(g_Core == NULL || g_Calculator == NULL) return;
    
    // 获取最新计算结果
    STradeCalculation calc;
    if(!g_Calculator.Calculate(calc))
    {
        Print("计算交易参数失败");
        return;
    }
    
    // 执行低点挂单
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = g_Core.ExecuteLowBuyStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    else if(order_type == ORDER_TYPE_SELL)
    {
        result = g_Core.ExecuteLowSellStop(calc.lot_size, calc.sl_price, calc.tp_price, calc.risk_percent);
    }
    
    if(result)
    {
        string direction = (order_type == ORDER_TYPE_BUY) ? "低点挂多" : "低点挂空";
        Print(StringFormat("挂单执行成功: %s %.2f手, SL:%.5f, TP:%.5f", 
                          direction, calc.lot_size, calc.sl_price, calc.tp_price));
    }
}
