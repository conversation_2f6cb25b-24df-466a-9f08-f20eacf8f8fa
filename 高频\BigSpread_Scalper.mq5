//+------------------------------------------------------------------+
//|                                        BigSpread_Scalper.mq5 |
//|                    💎 大点差剥头皮EA - 专为高点差平台设计        |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "1.0"
#property description "专门针对大点差平台的中高频交易EA"

//--- 输入参数
input group "=== 💎 大点差优化参数 ==="
input double   LotSize = 0.01;              // 交易手数
input int      MagicNumber = 666666;        // 魔术数字
input double   MinProfitPoints = 20.0;      // 最小盈利点数(必须>点差)
input double   MaxSpreadPoints = 200.0;     // 最大允许点差
input int      TimeframeMinutes = 1;        // 时间框架(分钟)

input group "=== ⚡ 趋势突破参数 ==="
input int      BreakoutPeriod = 20;         // 突破周期
input double   BreakoutMultiplier = 1.5;    // 突破倍数
input double   TrendStrength = 0.7;         // 趋势强度阈值
input bool     UseATRFilter = true;         // 使用ATR过滤

input group "=== 🎯 交易参数 ==="
input double   TakeProfit = 50.0;           // 止盈(点)
input double   StopLoss = 30.0;             // 止损(点)
input bool     UseTrailingStop = true;      // 使用移动止损
input double   TrailingStart = 25.0;        // 移动止损启动(点)
input double   TrailingStep = 10.0;         // 移动止损步长(点)

input group "=== 🛡️ 风控参数 ==="
input int      MaxPositions = 1;            // 最大持仓数
input double   MaxDailyLoss = 100.0;        // 日最大亏损($)
input bool     OnlyTradeTrends = true;      // 只交易趋势
input int      MinBarsSinceLastTrade = 5;   // 最小交易间隔(K线数)

//--- 全局变量
datetime last_trade_time = 0;
double daily_pnl = 0;
int total_trades = 0;
int winning_trades = 0;
double max_spread_seen = 0;

//+------------------------------------------------------------------+
//| EA初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    Print("💎💎💎 大点差剥头皮EA已启动！");
    Print("🎯 专为高点差平台优化");
    Print("📊 最小盈利: ", MinProfitPoints, " 点");
    Print("⚡ 时间框架: M", TimeframeMinutes);
    Print("🛡️ 最大点差: ", MaxSpreadPoints, " 点");
    Print("========================================");
    
    // 检查当前点差
    CheckCurrentSpread();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Tick函数
//+------------------------------------------------------------------+
void OnTick() {
    // 检查点差
    if(!CheckSpreadCondition()) return;
    
    // 风控检查
    if(!RiskManagementCheck()) return;
    
    // 管理现有持仓
    ManagePositions();
    
    // 检查新交易机会
    CheckTradingOpportunity();
}

//+------------------------------------------------------------------+
//| 检查点差条件
//+------------------------------------------------------------------+
bool CheckSpreadCondition() {
    double current_spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - 
                            SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    
    // 更新最大点差记录
    if(current_spread > max_spread_seen) {
        max_spread_seen = current_spread;
        PrintFormat("📊 新的最大点差记录: %.1f点", max_spread_seen);
    }
    
    if(current_spread > MaxSpreadPoints) {
        static datetime last_spread_warning = 0;
        if(TimeCurrent() - last_spread_warning > 300) {  // 5分钟警告一次
            PrintFormat("⚠️ 点差过大: %.1f点 > %.1f点，暂停交易", 
                       current_spread, MaxSpreadPoints);
            last_spread_warning = TimeCurrent();
        }
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查交易机会
//+------------------------------------------------------------------+
void CheckTradingOpportunity() {
    if(PositionsTotal() >= MaxPositions) return;
    
    // 检查交易间隔
    int bars_since_trade = iBarShift(_Symbol, PERIOD_M1, last_trade_time);
    if(bars_since_trade < MinBarsSinceLastTrade) return;
    
    // 获取市场数据
    double atr = iATR(_Symbol, PERIOD_M1, 14, 1);
    double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) + 
                           SymbolInfoDouble(_Symbol, SYMBOL_BID)) / 2;
    
    // ATR过滤 - 只在波动足够大时交易
    if(UseATRFilter && atr < MinProfitPoints * _Point * 2) {
        return;
    }
    
    // 趋势突破策略
    if(DetectBreakout()) {
        ExecuteBreakoutTrade();
    }
    
    // 支撑阻力反弹策略
    if(DetectSupportResistanceBounce()) {
        ExecuteBounceTradeStrategy();
    }
}

//+------------------------------------------------------------------+
//| 检测突破信号
//+------------------------------------------------------------------+
bool DetectBreakout() {
    // 计算最近N根K线的高低点
    double highest = iHigh(_Symbol, PERIOD_M1, iHighest(_Symbol, PERIOD_M1, MODE_HIGH, BreakoutPeriod, 1));
    double lowest = iLow(_Symbol, PERIOD_M1, iLowest(_Symbol, PERIOD_M1, MODE_LOW, BreakoutPeriod, 1));
    double range = highest - lowest;
    
    double current_high = iHigh(_Symbol, PERIOD_M1, 0);
    double current_low = iLow(_Symbol, PERIOD_M1, 0);
    double current_close = iClose(_Symbol, PERIOD_M1, 0);
    
    // 向上突破
    if(current_high > highest && current_close > highest) {
        double breakout_strength = (current_close - highest) / range;
        if(breakout_strength > 0.1) {  // 突破强度足够
            PrintFormat("🚀 检测到向上突破! 强度: %.2f", breakout_strength);
            return true;
        }
    }
    
    // 向下突破
    if(current_low < lowest && current_close < lowest) {
        double breakout_strength = (lowest - current_close) / range;
        if(breakout_strength > 0.1) {
            PrintFormat("🔻 检测到向下突破! 强度: %.2f", breakout_strength);
            return true;
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 执行突破交易
//+------------------------------------------------------------------+
void ExecuteBreakoutTrade() {
    double current_price = iClose(_Symbol, PERIOD_M1, 0);
    double prev_high = iHigh(_Symbol, PERIOD_M1, 1);
    double prev_low = iLow(_Symbol, PERIOD_M1, 1);
    
    ENUM_ORDER_TYPE order_type;
    double entry_price;
    string reason;
    
    if(current_price > prev_high) {
        order_type = ORDER_TYPE_BUY;
        entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        reason = "向上突破";
    } else {
        order_type = ORDER_TYPE_SELL;
        entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        reason = "向下突破";
    }
    
    ExecuteTrade(order_type, entry_price, reason);
}

//+------------------------------------------------------------------+
//| 检测支撑阻力反弹
//+------------------------------------------------------------------+
bool DetectSupportResistanceBounce() {
    // 简化的支撑阻力检测
    double ma20 = iMA(_Symbol, PERIOD_M1, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    double ma50 = iMA(_Symbol, PERIOD_M1, 50, 0, MODE_SMA, PRICE_CLOSE, 1);
    double current_price = iClose(_Symbol, PERIOD_M1, 0);
    double prev_price = iClose(_Symbol, PERIOD_M1, 1);
    
    // 在均线附近的反弹
    double distance_to_ma20 = MathAbs(current_price - ma20) / _Point;
    double distance_to_ma50 = MathAbs(current_price - ma50) / _Point;
    
    // 如果价格接近重要均线且出现反弹
    if(distance_to_ma20 < 10 || distance_to_ma50 < 15) {
        // 检查反弹信号
        if(prev_price < ma20 && current_price > ma20) {
            PrintFormat("📈 MA20反弹信号 - 价格: %s, MA20: %s", 
                       DoubleToString(current_price, _Digits),
                       DoubleToString(ma20, _Digits));
            return true;
        }
        if(prev_price > ma20 && current_price < ma20) {
            PrintFormat("📉 MA20回落信号 - 价格: %s, MA20: %s", 
                       DoubleToString(current_price, _Digits),
                       DoubleToString(ma20, _Digits));
            return true;
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 执行反弹交易策略
//+------------------------------------------------------------------+
void ExecuteBounceTradeStrategy() {
    double current_price = iClose(_Symbol, PERIOD_M1, 0);
    double ma20 = iMA(_Symbol, PERIOD_M1, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    
    ENUM_ORDER_TYPE order_type;
    double entry_price;
    string reason;
    
    if(current_price > ma20) {
        order_type = ORDER_TYPE_BUY;
        entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        reason = "均线反弹-多";
    } else {
        order_type = ORDER_TYPE_SELL;
        entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        reason = "均线反弹-空";
    }
    
    ExecuteTrade(order_type, entry_price, reason);
}

//+------------------------------------------------------------------+
//| 执行交易
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type, double price, string reason) {
    double sl = 0, tp = 0;
    
    // 计算止损止盈
    if(order_type == ORDER_TYPE_BUY) {
        if(StopLoss > 0) sl = price - StopLoss * _Point;
        if(TakeProfit > 0) tp = price + TakeProfit * _Point;
    } else {
        if(StopLoss > 0) sl = price + StopLoss * _Point;
        if(TakeProfit > 0) tp = price - TakeProfit * _Point;
    }
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = order_type;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = reason;
    request.deviation = 5;  // 大点差环境下允许更大滑点
    
    if(OrderSend(request, result)) {
        total_trades++;
        last_trade_time = TimeCurrent();
        
        double current_spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - 
                                SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
        
        PrintFormat("💎 [#%d] %s | 价格: %s | 点差: %.1f | 原因: %s | Ticket: %d", 
                   total_trades,
                   (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出",
                   DoubleToString(price, _Digits),
                   current_spread,
                   reason,
                   result.order);
    } else {
        PrintFormat("❌ 交易失败: %s (错误: %d)", reason, GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 管理持仓
//+------------------------------------------------------------------+
void ManagePositions() {
    if(!UseTrailingStop) return;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                                  SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                                  SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_profit = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                                   (current_price - open_price) / _Point : 
                                   (open_price - current_price) / _Point;
            
            // 移动止损
            if(current_profit > TrailingStart) {
                double current_sl = PositionGetDouble(POSITION_SL);
                double new_sl = 0;
                
                if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                    new_sl = current_price - TrailingStep * _Point;
                    if(new_sl > current_sl) {
                        ModifyPosition(PositionGetInteger(POSITION_TICKET), new_sl);
                    }
                } else {
                    new_sl = current_price + TrailingStep * _Point;
                    if(current_sl == 0 || new_sl < current_sl) {
                        ModifyPosition(PositionGetInteger(POSITION_TICKET), new_sl);
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 修改持仓
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double new_sl) {
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = new_sl;
    request.tp = PositionGetDouble(POSITION_TP);
    
    if(OrderSend(request, result)) {
        Print("📈 移动止损已更新: ", DoubleToString(new_sl, _Digits));
    }
}

//+------------------------------------------------------------------+
//| 风险管理检查
//+------------------------------------------------------------------+
bool RiskManagementCheck() {
    // 简化的风控检查
    if(daily_pnl < -MaxDailyLoss) {
        static bool daily_warned = false;
        if(!daily_warned) {
            Print("🛑 达到日最大亏损限制: $", daily_pnl);
            daily_warned = true;
        }
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查当前点差
//+------------------------------------------------------------------+
void CheckCurrentSpread() {
    double current_spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - 
                            SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    
    PrintFormat("📊 当前点差: %.1f点", current_spread);
    
    if(current_spread > 100) {
        Print("⚠️ 检测到大点差环境，EA已优化适配");
    }
    
    if(current_spread > MaxSpreadPoints) {
        Print("🚨 点差超过限制，请调整MaxSpreadPoints参数");
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result) {
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        if(trans.profit > 0) {
            winning_trades++;
        }
        daily_pnl += trans.profit;
        
        double win_rate = total_trades > 0 ? (double)winning_trades / total_trades * 100 : 0;
        PrintFormat("📊 交易统计 - 总数: %d | 胜率: %.1f%% | 日盈亏: $%.2f | 最大点差: %.1f", 
                   total_trades, win_rate, daily_pnl, max_spread_seen);
    }
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    double win_rate = total_trades > 0 ? (double)winning_trades / total_trades * 100 : 0;
    
    Print("========================================");
    Print("💎 大点差剥头皮EA已停止");
    PrintFormat("📊 总交易数: %d", total_trades);
    PrintFormat("🎯 胜率: %.1f%%", win_rate);
    PrintFormat("💰 日盈亏: $%.2f", daily_pnl);
    PrintFormat("📈 遇到的最大点差: %.1f点", max_spread_seen);
    Print("========================================");
}