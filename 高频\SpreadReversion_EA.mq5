//+------------------------------------------------------------------+
//|                                        SpreadReversion_EA.mq5 |
//|                    🚀 价差回归高频EA - 基于Tick级别交易        |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "1.0"
#property description "基于价差回归的超高频交易EA"

//--- 输入参数
input group "=== 🚀 核心交易参数 ==="
input double   LotSize = 0.01;              // 交易手数
input int      MagicNumber = 888888;        // 魔术数字
input double   SpreadThreshold = 3.0;       // 价差阈值(点)
input double   TakeProfit = 2.0;            // 止盈(点)
input double   StopLoss = 5.0;              // 止损(点)

input group "=== ⚡ 高频控制参数 ==="
input int      MaxPositions = 1;            // 最大持仓数
input int      MinTickInterval = 10;        // 最小tick间隔(毫秒)
input double   MaxSpreadPoints = 5.0;       // 最大允许价差(点)

input group "=== 🛡️ 风控参数 ==="
input double   MaxDailyLoss = 100.0;        // 日最大亏损($)
input int      MaxConsecutiveLosses = 3;    // 最大连续亏损次数
input bool     EnableTimeFilter = true;     // 启用时间过滤
input int      StartHour = 8;               // 开始交易时间
input int      EndHour = 22;                // 结束交易时间

//--- 全局变量
double prev_bid = 0;
double prev_ask = 0;
datetime prev_time = 0;
long tick_counter = 0;
datetime start_time = 0;

// 交易统计
int total_trades = 0;
int winning_trades = 0;
int consecutive_losses = 0;
double daily_pnl = 0;
datetime last_trade_date = 0;

// 价差统计
double spread_sum = 0;
int spread_count = 0;
double avg_spread = 0;

//+------------------------------------------------------------------+
//| EA初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    // 设置超高频定时器
    EventSetMillisecondTimer(1);
    start_time = TimeCurrent();
    tick_counter = 0;
    
    // 重置日统计
    ResetDailyStats();
    
    Print("🚀🚀🚀 价差回归高频EA已启动！");
    Print("⚡ 监控频率: 每1毫秒");
    Print("📊 交易品种: ", _Symbol);
    Print("💰 交易手数: ", LotSize);
    Print("🎯 价差阈值: ", SpreadThreshold, " 点");
    Print("🛡️ 风控已激活");
    Print("========================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Tick函数 - 每个tick都会触发
//+------------------------------------------------------------------+
void OnTick() {
    UltraHighFrequencyAnalysis();
}

//+------------------------------------------------------------------+
//| 超高频分析函数
//+------------------------------------------------------------------+
void UltraHighFrequencyAnalysis() {
    MqlTick current_tick;
    if(!SymbolInfoTick(_Symbol, current_tick)) return;
    
    tick_counter++;
    
    // 检查是否是新的一天
    MqlDateTime current_dt, last_dt;
    TimeToStruct(TimeCurrent(), current_dt);
    TimeToStruct(last_trade_date, last_dt);
    if(current_dt.day != last_dt.day) {
        ResetDailyStats();
    }
    
    // 风控检查
    if(!RiskManagementCheck()) return;
    
    // 时间过滤
    if(!TimeFilter()) return;
    
    // 初始化
    if(prev_bid == 0 && prev_ask == 0) {
        prev_bid = current_tick.bid;
        prev_ask = current_tick.ask;
        prev_time = current_tick.time;
        return;
    }
    
    // 计算当前价差
    double current_spread = (current_tick.ask - current_tick.bid) / _Point;
    
    // 更新价差统计
    UpdateSpreadStats(current_spread);
    
    // 检查价差异常
    if(current_spread > MaxSpreadPoints) {
        Print("⚠️ 价差过大: ", current_spread, " 点，跳过交易");
        return;
    }
    
    // 计算时间间隔
    long time_diff_ms = (long)(current_tick.time - prev_time) * 1000;
    if(time_diff_ms < MinTickInterval && time_diff_ms > 0) return;
    
    // 核心交易逻辑
    AnalyzeSpreadReversion(current_tick, current_spread);
    
    // 更新历史数据
    prev_bid = current_tick.bid;
    prev_ask = current_tick.ask;
    prev_time = current_tick.time;
}

//+------------------------------------------------------------------+
//| 价差回归分析
//+------------------------------------------------------------------+
void AnalyzeSpreadReversion(MqlTick &tick, double spread) {
    // 检查是否已有持仓
    if(PositionsTotal() >= MaxPositions) return;
    
    // 价差回归信号
    bool spread_too_wide = (spread > SpreadThreshold && avg_spread > 0 && spread > avg_spread * 1.5);
    bool spread_too_narrow = (spread < avg_spread * 0.5 && avg_spread > 1.0);
    
    if(spread_too_wide) {
        // 价差过宽，预期收窄 - 同时买入bid卖出ask
        ExecuteSpreadTrade(ORDER_TYPE_BUY, tick, "价差过宽-买入");
    }
    else if(spread_too_narrow) {
        // 价差过窄，预期扩大 - 等待更好机会或小心操作
        ExecuteSpreadTrade(ORDER_TYPE_SELL, tick, "价差过窄-卖出");
    }
}

//+------------------------------------------------------------------+
//| 执行价差交易
//+------------------------------------------------------------------+
void ExecuteSpreadTrade(ENUM_ORDER_TYPE order_type, MqlTick &tick, string reason) {
    double price = (order_type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
    double sl = 0, tp = 0;
    
    // 计算止损止盈
    if(order_type == ORDER_TYPE_BUY) {
        if(StopLoss > 0) sl = price - StopLoss * _Point;
        if(TakeProfit > 0) tp = price + TakeProfit * _Point;
    } else {
        if(StopLoss > 0) sl = price + StopLoss * _Point;
        if(TakeProfit > 0) tp = price - TakeProfit * _Point;
    }
    
    // 发送订单
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = order_type;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = reason;
    request.deviation = 3;
    
    if(OrderSend(request, result)) {
        total_trades++;
        PrintFormat("✅ [#%d] %s | 价格: %s | 原因: %s | Ticket: %d", 
                   total_trades,
                   (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出",
                   DoubleToString(price, _Digits),
                   reason,
                   result.order);
    } else {
        PrintFormat("❌ 交易失败: %s (错误: %d)", reason, GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 风险管理检查
//+------------------------------------------------------------------+
bool RiskManagementCheck() {
    // 检查日亏损限制
    if(daily_pnl < -MaxDailyLoss) {
        static bool daily_limit_warned = false;
        if(!daily_limit_warned) {
            Print("🛑 达到日最大亏损限制: $", daily_pnl);
            daily_limit_warned = true;
        }
        return false;
    }
    
    // 检查连续亏损
    if(consecutive_losses >= MaxConsecutiveLosses) {
        static bool consecutive_limit_warned = false;
        if(!consecutive_limit_warned) {
            Print("🛑 达到最大连续亏损次数: ", consecutive_losses);
            consecutive_limit_warned = true;
        }
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 时间过滤
//+------------------------------------------------------------------+
bool TimeFilter() {
    if(!EnableTimeFilter) return true;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    return (dt.hour >= StartHour && dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| 更新价差统计
//+------------------------------------------------------------------+
void UpdateSpreadStats(double spread) {
    spread_sum += spread;
    spread_count++;
    avg_spread = spread_sum / spread_count;
    
    // 每1000个tick输出一次统计
    if(spread_count % 1000 == 0) {
        PrintFormat("📊 价差统计 - 平均: %.1f点 | 当前: %.1f点 | 样本: %d", 
                   avg_spread, spread, spread_count);
    }
}

//+------------------------------------------------------------------+
//| 重置日统计
//+------------------------------------------------------------------+
void ResetDailyStats() {
    daily_pnl = 0;
    consecutive_losses = 0;
    last_trade_date = TimeCurrent();
    Print("📅 日统计已重置");
}

//+------------------------------------------------------------------+
//| 交易事件处理
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result) {
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        // 更新交易统计
        double profit = trans.price; // 这里需要计算实际盈亏
        daily_pnl += profit;
        
        if(profit > 0) {
            winning_trades++;
            consecutive_losses = 0;
        } else {
            consecutive_losses++;
        }
        
        double win_rate = total_trades > 0 ? (double)winning_trades / total_trades * 100 : 0;
        PrintFormat("📈 交易统计 - 总数: %d | 胜率: %.1f%% | 日盈亏: $%.2f", 
                   total_trades, win_rate, daily_pnl);
    }
}

//+------------------------------------------------------------------+
//| 定时器函数
//+------------------------------------------------------------------+
void OnTimer() {
    // 可以在这里添加定期检查逻辑
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    EventKillTimer();
    
    double win_rate = total_trades > 0 ? (double)winning_trades / total_trades * 100 : 0;
    
    Print("========================================");
    Print("🏁 价差回归EA已停止");
    PrintFormat("📊 总交易数: %d", total_trades);
    PrintFormat("🎯 胜率: %.1f%%", win_rate);
    PrintFormat("💰 日盈亏: $%.2f", daily_pnl);
    PrintFormat("📈 平均价差: %.1f点", avg_spread);
    Print("========================================");
}