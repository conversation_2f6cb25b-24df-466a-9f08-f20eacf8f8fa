# 数据处理和科学计算
numpy>=1.24.0
pandas>=1.5.3
scikit-learn>=1.2.0
scipy>=1.10.0

# MT5交易相关
MetaTrader5>=5.0.45

# 币安交易相关
python-binance>=1.0.0

# 消息推送
wxpusher>=2.2.0

# 任务调度
schedule>=1.2.0

# 环境变量管理
python-dotenv==1.0.0

# HTTP请求
requests>=2.28.0

# 日志和系统相关
pytz>=2023.3

# 国内期货数据
efinance>=0.5.1

# 创建requirements.txt
python-telegram-bot==20.7
tronpy==0.4.0
APScheduler==3.10.4

# PyGithub
PyGithub>=2.1.1

#Mysql
mysql-connector-python