//+------------------------------------------------------------------+
//|                                       VolumeAnomaly_EA.mq5 |
//|                    📊 成交量异常检测EA - 跟踪大资金动向        |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "1.0"
#property description "基于成交量异常的高频跟随EA"

//--- 输入参数
input group "=== 📊 成交量参数 ==="
input double   LotSize = 0.01;              // 交易手数
input int      MagicNumber = 777777;        // 魔术数字
input double   VolumeMultiplier = 3.0;      // 异常成交量倍数
input int      VolumePeriod = 50;           // 成交量统计周期
input double   MinPriceImpact = 1.5;        // 最小价格冲击(点)

input group "=== 🎯 跟随参数 ==="
input double   TakeProfit = 4.0;            // 止盈(点)
input double   StopLoss = 6.0;              // 止损(点)
input int      FollowDelay = 3;             // 跟随延迟(tick数)
input bool     UseTrailingStop = true;      // 使用移动止损
input double   TrailingDistance = 2.0;      // 移动止损距离(点)

input group "=== 🛡️ 风控参数 ==="
input int      MaxPositions = 3;            // 最大持仓数
input double   MaxDailyLoss = 200.0;        // 日最大亏损($)
input bool     OnlyFollowLarge = true;      // 只跟随大单
input double   LargeVolumeThreshold = 5.0;  // 大单阈值倍数

//--- 全局变量
struct VolumeData {
    datetime time;
    long volume;
    double price;
    double price_change;
    bool is_anomaly;
};

VolumeData volume_history[200];
int volume_index = 0;
double avg_volume = 0;
double volume_sum = 0;
int volume_count = 0;

// 异常检测
int anomaly_count = 0;
int successful_follows = 0;
double daily_pnl = 0;

//+------------------------------------------------------------------+
//| EA初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    EventSetMillisecondTimer(1);
    
    ArrayInitialize(volume_history, 0);
    
    Print("📊📊📊 成交量异常检测EA已启动！");
    Print("🔍 异常倍数: ", VolumeMultiplier, "x");
    Print("📈 统计周期: ", VolumePeriod, " tick");
    Print("🎯 专注跟踪大资金流向！");
    Print("========================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Tick函数
//+------------------------------------------------------------------+
void OnTick() {
    MqlTick current_tick;
    if(!SymbolInfoTick(_Symbol, current_tick)) return;
    
    // 更新成交量历史
    UpdateVolumeHistory(current_tick);
    
    // 检测成交量异常
    if(DetectVolumeAnomaly(current_tick)) {
        // 分析价格冲击
        AnalyzePriceImpact(current_tick);
    }
    
    // 管理现有持仓
    ManagePositions();
}

//+------------------------------------------------------------------+
//| 更新成交量历史
//+------------------------------------------------------------------+
void UpdateVolumeHistory(MqlTick &tick) {
    double mid_price = (tick.bid + tick.ask) / 2.0;
    
    // 计算价格变化
    double price_change = 0;
    if(volume_index > 0) {
        int prev_index = (volume_index - 1) % 200;
        price_change = mid_price - volume_history[prev_index].price;
    }
    
    // 存储数据
    int current_index = volume_index % 200;
    volume_history[current_index].time = tick.time;
    volume_history[current_index].volume = tick.volume;
    volume_history[current_index].price = mid_price;
    volume_history[current_index].price_change = price_change;
    volume_history[current_index].is_anomaly = false;
    
    // 更新平均成交量
    volume_sum += tick.volume;
    volume_count++;
    avg_volume = volume_sum / volume_count;
    
    // 限制历史数据量，避免内存过大
    if(volume_count > 10000) {
        volume_sum = avg_volume * VolumePeriod;
        volume_count = VolumePeriod;
    }
    
    volume_index++;
}

//+------------------------------------------------------------------+
//| 检测成交量异常
//+------------------------------------------------------------------+
bool DetectVolumeAnomaly(MqlTick &tick) {
    if(volume_count < VolumePeriod) return false;
    
    // 计算最近周期的平均成交量
    double recent_avg = CalculateRecentAverage();
    
    // 检测异常
    bool is_anomaly = (tick.volume > recent_avg * VolumeMultiplier);
    
    if(OnlyFollowLarge) {
        is_anomaly = is_anomaly && (tick.volume > avg_volume * LargeVolumeThreshold);
    }
    
    if(is_anomaly) {
        int current_index = (volume_index - 1) % 200;
        volume_history[current_index].is_anomaly = true;
        anomaly_count++;
        
        PrintFormat("🚨 成交量异常! 当前: %ld | 平均: %.0f | 倍数: %.1fx | 价格: %s", 
                   tick.volume, recent_avg, 
                   tick.volume / recent_avg,
                   DoubleToString((tick.bid + tick.ask) / 2, _Digits));
    }
    
    return is_anomaly;
}

//+------------------------------------------------------------------+
//| 计算最近平均成交量
//+------------------------------------------------------------------+
double CalculateRecentAverage() {
    if(volume_index < VolumePeriod) return avg_volume;
    
    long sum = 0;
    int start_index = MathMax(0, volume_index - VolumePeriod);
    
    for(int i = start_index; i < volume_index; i++) {
        int index = i % 200;
        sum += volume_history[index].volume;
    }
    
    return (double)sum / VolumePeriod;
}

//+------------------------------------------------------------------+
//| 分析价格冲击
//+------------------------------------------------------------------+
void AnalyzePriceImpact(MqlTick &tick) {
    if(volume_index < FollowDelay + 1) return;
    if(PositionsTotal() >= MaxPositions) return;
    
    // 计算价格冲击方向和强度
    double price_impact = CalculatePriceImpact();
    
    if(MathAbs(price_impact) < MinPriceImpact * _Point) {
        Print("💡 价格冲击不足，跳过跟随");
        return;
    }
    
    // 确定跟随方向
    ENUM_ORDER_TYPE order_type = (price_impact > 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    string reason = StringFormat("跟随大单-%s冲击%.1f点", 
                                (price_impact > 0) ? "上" : "下",
                                MathAbs(price_impact) / _Point);
    
    // 执行跟随交易
    ExecuteFollowTrade(order_type, tick, reason);
}

//+------------------------------------------------------------------+
//| 计算价格冲击
//+------------------------------------------------------------------+
double CalculatePriceImpact() {
    if(volume_index < FollowDelay + 1) return 0;
    
    // 获取异常成交量前后的价格
    int anomaly_index = (volume_index - 1) % 200;
    int before_index = (volume_index - FollowDelay - 1) % 200;
    
    double price_before = volume_history[before_index].price;
    double price_after = volume_history[anomaly_index].price;
    
    return price_after - price_before;
}

//+------------------------------------------------------------------+
//| 执行跟随交易
//+------------------------------------------------------------------+
void ExecuteFollowTrade(ENUM_ORDER_TYPE order_type, MqlTick &tick, string reason) {
    double price = (order_type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
    double sl = 0, tp = 0;
    
    // 计算止损止盈
    if(order_type == ORDER_TYPE_BUY) {
        if(StopLoss > 0) sl = price - StopLoss * _Point;
        if(TakeProfit > 0) tp = price + TakeProfit * _Point;
    } else {
        if(StopLoss > 0) sl = price + StopLoss * _Point;
        if(TakeProfit > 0) tp = price - TakeProfit * _Point;
    }
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = order_type;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = MagicNumber;
    request.comment = reason;
    request.deviation = 3;
    
    if(OrderSend(request, result)) {
        PrintFormat("📊 [跟随] %s | 价格: %s | 原因: %s | Ticket: %d", 
                   (order_type == ORDER_TYPE_BUY) ? "买入" : "卖出",
                   DoubleToString(price, _Digits),
                   reason,
                   result.order);
    } else {
        PrintFormat("❌ 跟随交易失败: %s (错误: %d)", reason, GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 管理持仓
//+------------------------------------------------------------------+
void ManagePositions() {
    if(!UseTrailingStop) return;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                                  SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                                  SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            
            // 计算新的移动止损
            double new_sl = 0;
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                new_sl = current_price - TrailingDistance * _Point;
                if(new_sl > current_sl && new_sl < current_price) {
                    ModifyPosition(PositionGetInteger(POSITION_TICKET), new_sl);
                }
            } else {
                new_sl = current_price + TrailingDistance * _Point;
                if((current_sl == 0 || new_sl < current_sl) && new_sl > current_price) {
                    ModifyPosition(PositionGetInteger(POSITION_TICKET), new_sl);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 修改持仓
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double new_sl) {
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = new_sl;
    request.tp = PositionGetDouble(POSITION_TP);
    
    if(OrderSend(request, result)) {
        Print("📈 移动止损已更新: ", DoubleToString(new_sl, _Digits));
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result) {
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        if(trans.profit > 0) {
            successful_follows++;
        }
        daily_pnl += trans.profit;
        
        double success_rate = anomaly_count > 0 ? (double)successful_follows / anomaly_count * 100 : 0;
        PrintFormat("📊 跟随统计 - 异常: %d | 成功: %d | 成功率: %.1f%% | 日盈亏: $%.2f", 
                   anomaly_count, successful_follows, success_rate, daily_pnl);
    }
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    EventKillTimer();
    
    double success_rate = anomaly_count > 0 ? (double)successful_follows / anomaly_count * 100 : 0;
    
    Print("========================================");
    Print("📊 成交量异常EA已停止");
    PrintFormat("🚨 检测异常: %d次", anomaly_count);
    PrintFormat("✅ 成功跟随: %d次", successful_follows);
    PrintFormat("🎯 成功率: %.1f%%", success_rate);
    PrintFormat("💰 日盈亏: $%.2f", daily_pnl);
    Print("========================================");
}